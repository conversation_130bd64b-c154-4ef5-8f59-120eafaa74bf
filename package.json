{"name": "yogabot", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.9.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@types/uuid": "^8.3.4", "@upstash/qstash": "^2.8.1", "@xenova/transformers": "^2.17.2", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "next": "15.3.3", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "shadcn-ui": "^0.9.5", "tailwind-merge": "^3.3.1", "uuid": "^8.3.2", "zod": "^3.25.66"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv-cli": "^8.0.0", "eslint": "^9", "eslint-config-next": "15.3.3", "prisma": "^6.9.0", "tailwindcss": "^4.1.10", "typescript": "^5"}}