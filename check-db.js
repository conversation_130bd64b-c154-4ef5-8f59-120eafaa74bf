const { PrismaClient } = require('./src/generated/prisma');

const prisma = new PrismaClient();

async function checkConnection() {
  try {
    console.log('Attempting to connect to the database...');
    
    // Try to query users
    const users = await prisma.user.findMany();
    console.log(`Successfully connected to the database. Found ${users.length} users.`);
    
    if (users.length > 0) {
      console.log('User emails:');
      users.forEach(user => {
        console.log(`- ${user.email} (${user.role})`);
      });
    }
    
    // Check for plans
    const plans = await prisma.plan.findMany();
    console.log(`Found ${plans.length} plans.`);
    
    if (plans.length > 0) {
      console.log('Plans:');
      plans.forEach(plan => {
        console.log(`- ${plan.name}: $${plan.price / 100}`);
      });
    }
    
  } catch (error) {
    console.error('Error connecting to the database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkConnection(); 