const { PrismaClient } = require('./src/generated/prisma');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    console.log('Creating test user account...');
    
    // Create a basic plan if it doesn't exist
    const basicPlan = await prisma.plan.upsert({
      where: { name: 'Basic' },
      update: {},
      create: {
        name: 'Basic',
        price: 1999, // $19.99
        features: {
          kbType: 'simple',
          tokenLimit: 50000,
          canUseBYOK: false
        },
        isActive: true
      }
    });
    console.log(`Plan created/updated: ${basicPlan.name}`);
    
    // Create regular user account
    const userPassword = await bcrypt.hash('user123', 10);
    const user = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: { password: userPassword },
      create: {
        email: '<EMAIL>',
        name: 'Test User',
        password: userPassword,
        role: 'USER'
      }
    });
    console.log(`User account created/updated: ${user.email}`);
    
    // Create subscription for the user
    const subscription = await prisma.subscription.upsert({
      where: { userId: user.id },
      update: {
        planId: basicPlan.id,
        status: 'active',
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
      },
      create: {
        userId: user.id,
        planId: basicPlan.id,
        status: 'active',
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        tokensUsedThisPeriod: 0,
        sessionsThisPeriod: 0
      }
    });
    console.log(`Subscription created/updated for user: ${user.email}`);
    
    // Create chatbot for the user if it doesn't exist
    const existingChatbot = await prisma.chatbot.findFirst({
      where: { userId: user.id }
    });
    
    if (!existingChatbot) {
      const chatbot = await prisma.chatbot.create({
        data: {
          userId: user.id,
          approvedDomain: 'localhost',
          systemPrompt: 'You are a helpful yoga assistant.',
          llmProvider: 'gemini',
          llmModel: 'gemini-pro'
        }
      });
      console.log(`Chatbot created for user: ${user.email}`);
    } else {
      console.log(`Chatbot already exists for user: ${user.email}`);
    }
    
    console.log('Test user created successfully!');
    console.log('User login credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: user123');
    
  } catch (error) {
    console.error('Error creating test user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser(); 