const { PrismaClient } = require('./src/generated/prisma');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createAdmin() {
  try {
    console.log('Creating admin account...');
    
    // Create admin account
    const adminPassword = await bcrypt.hash('admin123', 10);
    const admin = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: { password: adminPassword },
      create: {
        email: '<EMAIL>',
        name: 'Admin User',
        password: adminPassword,
        role: 'ADMIN'
      }
    });
    console.log(`Admin account created/updated: ${admin.email}`);
    console.log('Admin login credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: admin123');
    
  } catch (error) {
    console.error('Error creating admin account:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createAdmin(); 