import { Client } from '@upstash/qstash';

// Initialize the QStash client
export const qstashClient = new Client({
  token: process.env.QSTASH_TOKEN || '',
});

// Helper function to publish a job to QStash
export async function publishJob(url: string, body: any, options?: {
  delay?: number; // Delay in seconds
  retries?: number; // Number of retries
}) {
  try {
    const response = await qstashClient.publishJSON({
      url,
      body,
      retries: options?.retries || 3,
      delay: options?.delay || 0,
    });
    
    return { success: true, messageId: response.messageId };
  } catch (error) {
    console.error('Error publishing job to QStash:', error);
    return { success: false, error };
  }
} 