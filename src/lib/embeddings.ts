import { pipeline } from '@xenova/transformers';

// Define the embedding model to use
const MODEL_NAME = 'Xenova/all-MiniLM-L6-v2';

// Cache the pipeline to avoid reloading the model on every request
let embeddingPipeline: any = null;

/**
 * Generates embeddings for a given text using the transformers.js library
 * @param text The text to generate embeddings for
 * @returns A Float32Array containing the embedding vector
 */
export async function generateEmbedding(text: string): Promise<Float32Array> {
  try {
    // Initialize the pipeline if it hasn't been already
    if (!embeddingPipeline) {
      embeddingPipeline = await pipeline('feature-extraction', MODEL_NAME);
    }

    // Generate embeddings
    const result = await embeddingPipeline(text, {
      pooling: 'mean',
      normalize: true,
    });

    // Return the embedding vector
    return result.data;
  } catch (error) {
    console.error('Error generating embedding:', error);
    throw new Error('Failed to generate embedding');
  }
}

/**
 * Computes the cosine similarity between two embedding vectors
 * @param a First embedding vector
 * @param b Second embedding vector
 * @returns Similarity score between 0 and 1
 */
export function computeSimilarity(a: Float32Array, b: Float32Array): number {
  if (a.length !== b.length) {
    throw new Error('Embedding dimensions do not match');
  }

  let dotProduct = 0;
  let normA = 0;
  let normB = 0;

  for (let i = 0; i < a.length; i++) {
    dotProduct += a[i] * b[i];
    normA += a[i] * a[i];
    normB += b[i] * b[i];
  }

  normA = Math.sqrt(normA);
  normB = Math.sqrt(normB);

  return dotProduct / (normA * normB);
}

/**
 * Searches for the most similar chunks to a query in a list of chunks
 * @param query The query text
 * @param chunks Array of chunks with their embeddings
 * @param topK Number of results to return
 * @returns Array of chunks sorted by similarity
 */
export async function semanticSearch(
  query: string,
  chunks: Array<{ id: string; content: string; embedding: Float32Array }>,
  topK: number = 3
): Promise<Array<{ id: string; content: string; similarity: number }>> {
  try {
    // Generate embedding for the query
    const queryEmbedding = await generateEmbedding(query);

    // Calculate similarity scores
    const scoredChunks = chunks.map(chunk => ({
      id: chunk.id,
      content: chunk.content,
      similarity: computeSimilarity(queryEmbedding, chunk.embedding),
    }));

    // Sort by similarity (highest first) and take top K
    return scoredChunks
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, topK);
  } catch (error) {
    console.error('Error in semantic search:', error);
    throw new Error('Failed to perform semantic search');
  }
} 