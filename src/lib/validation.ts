import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

/**
 * Validates request data against a Zod schema and returns the validated data or an error response
 */
export async function validateRequest<T extends z.ZodType>(
  req: NextRequest,
  schema: T
): Promise<
  | { success: true; data: z.infer<T> }
  | { success: false; error: NextResponse<{ error: string }> }
> {
  try {
    // Try to parse the request body as JSON
    const body = await req.json();
    const validatedData = schema.parse(body);
    return { success: true, data: validatedData };
  } catch (error) {
    // If it's a Zod error, format it nicely
    if (error instanceof z.ZodError) {
      const formattedErrors = error.errors.map((err) => {
        return `${err.path.join(".")}: ${err.message}`;
      }).join(", ");
      
      return {
        success: false,
        error: NextResponse.json(
          { error: `Validation error: ${formattedErrors}` },
          { status: 400 }
        ),
      };
    }
    
    // For other errors, return a generic error message
    return {
      success: false,
      error: NextResponse.json(
        { error: "Invalid request data" },
        { status: 400 }
      ),
    };
  }
}

/**
 * Validates URL parameters against a Zod schema
 */
export function validateParams<T extends z.ZodType>(
  params: Record<string, string | string[] | undefined>,
  schema: T
): { success: true; data: z.infer<T> } | { success: false; error: string } {
  try {
    const validatedData = schema.parse(params);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const formattedErrors = error.errors.map((err) => {
        return `${err.path.join(".")}: ${err.message}`;
      }).join(", ");
      
      return {
        success: false,
        error: `Validation error: ${formattedErrors}`,
      };
    }
    
    return {
      success: false,
      error: "Invalid parameters",
    };
  }
}

/**
 * Common validation schemas
 */
export const validationSchemas = {
  // Plan schemas
  plan: {
    create: z.object({
      name: z.string().min(1, "Name is required"),
      price: z.number().int().min(0, "Price must be a positive number"),
      features: z.object({
        kbType: z.enum(["simple", "structured"]),
        tokenLimit: z.number().int().min(1000, "Token limit must be at least 1000"),
        canUseBYOK: z.boolean(),
      }).or(z.string()),
      isActive: z.boolean().optional().default(true),
    }),
    update: z.object({
      name: z.string().min(1, "Name is required").optional(),
      price: z.number().int().min(0, "Price must be a positive number").optional(),
      features: z.object({
        kbType: z.enum(["simple", "structured"]),
        tokenLimit: z.number().int().min(1000, "Token limit must be at least 1000"),
        canUseBYOK: z.boolean(),
      }).or(z.string()).optional(),
      isActive: z.boolean().optional(),
    }),
    id: z.object({
      id: z.string().min(1, "Plan ID is required"),
    }),
  },
  
  // User schemas
  user: {
    create: z.object({
      email: z.string().email("Invalid email address"),
      name: z.string().min(1, "Name is required"),
      password: z.string().min(8, "Password must be at least 8 characters"),
      role: z.enum(["ADMIN", "USER"]).optional().default("USER"),
      planId: z.string().min(1, "Plan ID is required"),
      approvedDomain: z.string().min(1, "Approved domain is required"),
    }),
    update: z.object({
      email: z.string().email("Invalid email address").optional(),
      name: z.string().min(1, "Name is required").optional(),
      password: z.string().min(8, "Password must be at least 8 characters").optional(),
    }),
    id: z.object({
      id: z.string().min(1, "User ID is required"),
    }),
  },
  
  // Chatbot schemas
  chatbot: {
    update: z.object({
      systemPrompt: z.string().optional(),
      llmProvider: z.string().optional(),
      llmModel: z.string().optional(),
      encryptedLlmApiKey: z.string().optional(),
      widgetConfig: z.record(z.unknown()).or(z.string()).optional(),
      smtpConfig: z.record(z.unknown()).or(z.string()).optional(),
      approvedDomain: z.string().optional(),
    }),
    id: z.object({
      id: z.string().min(1, "Chatbot ID is required"),
    }),
  },
  
  // Knowledge base schemas
  knowledgeBase: {
    saveSimple: z.object({
      text: z.string().min(1, "Text is required"),
    }),
    saveStructured: z.object({
      // This will be a complex schema based on the form.md structure
      // We'll implement it when we build the structured form
    }),
  },
}; 