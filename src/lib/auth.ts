import { getServerSession } from "next-auth/next";
import { PrismaAdapter } from "@next-auth/prisma-adapter";
import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { compare } from "bcryptjs";
import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  session: {
    strategy: "jwt",
  },
  pages: {
    signIn: "/login",
  },
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email,
          },
        });

        if (!user) {
          return null;
        }

        const isPasswordValid = await compare(credentials.password, user.password);

        if (!isPasswordValid) {
          return null;
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
        };
      },
    }),
  ],
  callbacks: {
    async session({ token, session }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.name = token.name;
        session.user.email = token.email;
        session.user.role = token.role as string;
      }
      return session;
    },
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.role = user.role;
      }
      return token;
    },
  },
};

export const getSession = () => getServerSession(authOptions);

// Universal API Authorization utility
export async function authorizeRequest(
  req: NextRequest,
  {
    requiredRole = "USER",
    checkResourceOwnership = false,
    resourceId = "",
    resourceType = "",
  }: {
    requiredRole?: "ADMIN" | "USER";
    checkResourceOwnership?: boolean;
    resourceId?: string;
    resourceType?: string;
  } = {}
) {
  const session = await getSession();

  // Check if user is authenticated
  if (!session?.user) {
    return {
      authorized: false,
      message: "Unauthorized: Authentication required",
      status: 401,
    };
  }

  // Check if user has required role
  if (requiredRole === "ADMIN" && session.user.role !== "ADMIN") {
    return {
      authorized: false,
      message: "Forbidden: Admin access required",
      status: 403,
    };
  }

  // Check resource ownership if required
  if (checkResourceOwnership && resourceId && resourceType) {
    let isOwner = false;

    switch (resourceType) {
      case "chatbot":
        const chatbot = await prisma.chatbot.findUnique({
          where: { id: resourceId },
          select: { userId: true },
        });
        isOwner = chatbot?.userId === session.user.id;
        break;

      // Add more resource types as needed
      default:
        isOwner = false;
    }

    if (!isOwner && session.user.role !== "ADMIN") {
      return {
        authorized: false,
        message: "Forbidden: You don't have access to this resource",
        status: 403,
      };
    }
  }

  // Check subscription status if it's a resource-consuming operation
  if (req.method !== "GET" && session.user.role === "USER") {
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: { subscription: { include: { plan: true } } },
    });

    if (!user?.subscription) {
      return {
        authorized: false,
        message: "Forbidden: No active subscription",
        status: 403,
      };
    }

    if (user.subscription.status !== "active") {
      return {
        authorized: false,
        message: "Forbidden: Subscription is not active",
        status: 403,
      };
    }

    // Parse features from the string (since we're using SQLite in development)
    const features = JSON.parse(user.subscription.plan.features as string);
    
    // Check if user has exceeded their token limit
    if (
      user.subscription.tokensUsedThisPeriod >= features.tokenLimit
    ) {
      return {
        authorized: false,
        message: "Forbidden: Token limit exceeded for current billing period",
        status: 403,
      };
    }
  }

  return {
    authorized: true,
    userId: session.user.id,
    userRole: session.user.role,
    status: 200,
  };
}

// Helper function to handle unauthorized responses
export function handleUnauthorized(authResult: ReturnType<typeof authorizeRequest>) {
  if (!authResult.authorized) {
    return NextResponse.json(
      { error: authResult.message },
      { status: authResult.status }
    );
  }
  return null;
}

// Add NextAuth types
declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      name: string;
      email: string;
      role: string;
    };
  }

  interface User {
    role: string;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    role: string;
  }
} 