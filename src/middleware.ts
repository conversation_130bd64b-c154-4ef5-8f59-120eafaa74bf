import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getToken } from "next-auth/jwt";

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Public routes that don't require authentication
  const publicRoutes = [
    "/login",
    "/register",
    "/api/auth",
    "/widget",
  ];
  
  // Check if the path is a public route
  const isPublicRoute = publicRoutes.some((route) => pathname.startsWith(route));
  
  // Check if the path is for static files
  const isStaticFile = /\.(jpg|jpeg|png|gif|svg|css|js|ico|webp)$/i.test(pathname);
  
  // If it's a public route or a static file, allow access
  if (isPublicRoute || isStaticFile) {
    return NextResponse.next();
  }
  
  // Get the user's token
  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET,
  });
  
  // If there's no token and the user is trying to access a protected route, redirect to login
  if (!token) {
    const url = new URL("/login", request.url);
    url.searchParams.set("callbackUrl", encodeURI(request.url));
    return NextResponse.redirect(url);
  }
  
  // Admin routes check
  if (pathname.startsWith("/admin") && token.role !== "ADMIN") {
    return NextResponse.redirect(new URL("/dashboard", request.url));
  }
  
  // User is authenticated, allow access
  return NextResponse.next();
}

// Configure which paths the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!_next/static|_next/image|favicon.ico).*)",
  ],
}; 