import Link from 'next/link';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { redirect } from 'next/navigation';

export default async function Home() {
  const session = await getServerSession(authOptions);
  
  // If user is logged in, redirect to the appropriate dashboard
  if (session) {
    if (session.user.role === 'ADMIN') {
      redirect('/admin');
    } else {
      redirect('/dashboard');
    }
  }
  
  return (
    <div className="min-h-screen flex flex-col">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-500 to-purple-600 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">YogaBot Live</h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
            The AI-powered chatbot designed specifically for yoga schools.
            Engage with visitors 24/7, answer questions, and provide personalized guidance.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link
              href="/login"
              className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold text-lg"
            >
              Sign In
            </Link>
            <Link
              href="/contact"
              className="bg-transparent hover:bg-white hover:bg-opacity-20 border-2 border-white px-8 py-3 rounded-lg font-semibold text-lg"
            >
              Contact Sales
            </Link>
          </div>
        </div>
      </section>
      
      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Key Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-4">Customizable Knowledge Base</h3>
              <p className="text-gray-600">
                Train your chatbot with your school's specific information, class schedules, teacher bios, and more.
              </p>
            </div>
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-4">Live Chat Takeover</h3>
              <p className="text-gray-600">
                Monitor conversations in real-time and seamlessly take over from the AI when needed.
              </p>
            </div>
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-4">Easy Integration</h3>
              <p className="text-gray-600">
                Add the chatbot to your website with a simple embed code. No technical skills required.
              </p>
            </div>
          </div>
        </div>
      </section>
      
      {/* Plans Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-4">Subscription Plans</h2>
          <p className="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
            Choose the plan that best fits your school's needs. All plans include 24/7 chatbot availability.
          </p>
          <div className="flex flex-col md:flex-row justify-center gap-8">
            <div className="bg-white p-8 rounded-lg shadow-md flex-1 max-w-md">
              <h3 className="text-xl font-semibold mb-2">Basic</h3>
              <p className="text-3xl font-bold mb-4">$49<span className="text-lg text-gray-500">/month</span></p>
              <ul className="mb-8 space-y-2">
                <li className="flex items-center"><span className="mr-2 text-green-500">✓</span> Simple text knowledge base</li>
                <li className="flex items-center"><span className="mr-2 text-green-500">✓</span> 50,000 tokens per month</li>
                <li className="flex items-center"><span className="mr-2 text-green-500">✓</span> Basic chat monitoring</li>
              </ul>
              <Link
                href="/contact"
                className="block text-center bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg"
              >
                Contact Sales
              </Link>
            </div>
            <div className="bg-white p-8 rounded-lg shadow-md flex-1 max-w-md border-2 border-blue-500 relative">
              <div className="absolute top-0 right-0 bg-blue-500 text-white py-1 px-4 text-sm rounded-bl-lg">POPULAR</div>
              <h3 className="text-xl font-semibold mb-2">Pro</h3>
              <p className="text-3xl font-bold mb-4">$99<span className="text-lg text-gray-500">/month</span></p>
              <ul className="mb-8 space-y-2">
                <li className="flex items-center"><span className="mr-2 text-green-500">✓</span> Structured form knowledge base</li>
                <li className="flex items-center"><span className="mr-2 text-green-500">✓</span> 200,000 tokens per month</li>
                <li className="flex items-center"><span className="mr-2 text-green-500">✓</span> Live chat takeover</li>
                <li className="flex items-center"><span className="mr-2 text-green-500">✓</span> Visitor analytics</li>
              </ul>
              <Link
                href="/contact"
                className="block text-center bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg"
              >
                Contact Sales
              </Link>
            </div>
          </div>
        </div>
      </section>
      
      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8 mt-auto">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <h3 className="text-xl font-bold">YogaBot Live</h3>
              <p className="text-gray-400">© 2025 All rights reserved</p>
            </div>
            <div className="flex gap-4">
              <Link href="/login" className="text-gray-300 hover:text-white">Login</Link>
              <Link href="/contact" className="text-gray-300 hover:text-white">Contact</Link>
              <Link href="/privacy" className="text-gray-300 hover:text-white">Privacy Policy</Link>
              <Link href="/terms" className="text-gray-300 hover:text-white">Terms of Service</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
