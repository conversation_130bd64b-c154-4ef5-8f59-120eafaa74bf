'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { redirect } from 'next/navigation';
import Link from 'next/link';

interface UserData {
  id: string;
  name: string;
  email: string;
  subscription: {
    status: string;
    currentPeriodEnd: string;
    tokensUsedThisPeriod: number;
    plan: {
      name: string;
      features: {
        kbType: 'simple' | 'structured';
        tokenLimit: number;
        canUseBYOK: boolean;
      };
    };
  };
  chatbot: {
    id: string;
    approvedDomain: string;
  };
}

export default function Dashboard() {
  const { data: session, status } = useSession();
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      redirect('/login');
    }
  }, [status]);

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      if (status !== 'authenticated') return;

      try {
        setLoading(true);
        const response = await fetch('/api/user/profile');
        
        if (!response.ok) {
          throw new Error('Failed to fetch user data');
        }
        
        const data = await response.json();
        setUserData(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching user data:', err);
        setError('Failed to load your profile data');
      } finally {
        setLoading(false);
      }
    };
    
    fetchUserData();
  }, [status]);

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (status === 'loading' || loading) {
    return <div className="p-8 text-center">Loading...</div>;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-8">Dashboard</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {/* User Info Card */}
      <div className="bg-white p-6 rounded-lg shadow-md mb-8">
        <h2 className="text-xl font-semibold mb-4">Welcome, {session?.user?.name || 'User'}</h2>
        
        {userData && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-gray-600">Email:</p>
              <p className="font-medium">{userData.email}</p>
            </div>
            
            <div>
              <p className="text-gray-600">Subscription Plan:</p>
              <p className="font-medium">{userData.subscription?.plan.name || 'No Plan'}</p>
            </div>
            
            <div>
              <p className="text-gray-600">Status:</p>
              <p className="font-medium">
                <span
                  className={`px-2 py-1 rounded text-xs ${
                    userData.subscription?.status === 'active'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}
                >
                  {userData.subscription?.status || 'No Subscription'}
                </span>
              </p>
            </div>
            
            <div>
              <p className="text-gray-600">Expires:</p>
              <p className="font-medium">
                {userData.subscription
                  ? formatDate(userData.subscription.currentPeriodEnd)
                  : 'N/A'}
              </p>
            </div>
            
            <div>
              <p className="text-gray-600">Token Usage:</p>
              <p className="font-medium">
                {userData.subscription
                  ? `${userData.subscription.tokensUsedThisPeriod.toLocaleString()} / ${userData.subscription.plan.features.tokenLimit.toLocaleString()}`
                  : 'N/A'}
              </p>
            </div>
            
            <div>
              <p className="text-gray-600">Approved Domain:</p>
              <p className="font-medium">{userData.chatbot?.approvedDomain || 'N/A'}</p>
            </div>
          </div>
        )}
      </div>
      
      {/* Quick Actions */}
      <h2 className="text-2xl font-semibold mb-4">Quick Actions</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Link href="/dashboard/kb" className="bg-blue-100 hover:bg-blue-200 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Knowledge Base</h3>
          <p className="text-gray-600">Manage your chatbot's knowledge base</p>
        </Link>
        
        <Link href="/dashboard/chat" className="bg-green-100 hover:bg-green-200 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Live Chat</h3>
          <p className="text-gray-600">Monitor and take over live chat sessions</p>
        </Link>
        
        <Link href="/dashboard/settings" className="bg-purple-100 hover:bg-purple-200 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Settings</h3>
          <p className="text-gray-600">Configure your chatbot and account settings</p>
        </Link>
      </div>
      
      {/* Widget Embed Code */}
      <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Embed Your Chatbot</h2>
        <p className="mb-4">
          Copy and paste this code snippet into your website to embed your chatbot:
        </p>
        <div className="bg-gray-100 p-4 rounded-lg overflow-x-auto">
          <code className="text-sm">
            {userData?.chatbot ? (
              `<iframe src="${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/widget/${userData.chatbot.id}" width="400" height="600" frameborder="0"></iframe>`
            ) : (
              'Loading embed code...'
            )}
          </code>
        </div>
      </div>
    </div>
  );
} 