'use client';

import { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';

// School Brand Form Tab
import SchoolBrandForm from './form-tabs/school-brand';
// School Contact Form Tab
import SchoolContactForm from './form-tabs/school-contact';
// Teachers Form Tab
import TeachersForm from './form-tabs/teachers';
// TTC Form Tab
import TTCForm from './form-tabs/ttc';
// Retreats Form Tab
import RetreatsForm from './form-tabs/retreats';
// Online Courses Form Tab
import OnlineCoursesForm from './form-tabs/online-courses';
// Facilities Form Tab
import FacilitiesForm from './form-tabs/facilities';
// Policies Form Tab
import PoliciesForm from './form-tabs/policies';
// FAQ Form Tab
import FAQForm from './form-tabs/faq';

export interface StructuredFormProps {
  chatbotId: string;
}

export default function StructuredForm({ chatbotId }: StructuredFormProps) {
  const [activeTab, setActiveTab] = useState('brand');
  const [saving, setSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle');

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-4">Structured Knowledge Base</h2>
      <p className="mb-6 text-gray-600">
        Fill out the sections below to provide detailed information about your yoga school.
        This structured approach helps the AI provide more accurate and specific answers to your visitors.
      </p>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-3 md:grid-cols-6 lg:grid-cols-9 mb-8">
          <TabsTrigger value="brand" className="text-xs md:text-sm">
            School Identity
          </TabsTrigger>
          <TabsTrigger value="contact" className="text-xs md:text-sm">
            Contact & Location
          </TabsTrigger>
          <TabsTrigger value="teachers" className="text-xs md:text-sm">
            Teachers
          </TabsTrigger>
          <TabsTrigger value="ttc" className="text-xs md:text-sm">
            Training Courses
          </TabsTrigger>
          <TabsTrigger value="retreats" className="text-xs md:text-sm">
            Retreats
          </TabsTrigger>
          <TabsTrigger value="online" className="text-xs md:text-sm">
            Online Offerings
          </TabsTrigger>
          <TabsTrigger value="facilities" className="text-xs md:text-sm">
            Facilities
          </TabsTrigger>
          <TabsTrigger value="policies" className="text-xs md:text-sm">
            Policies
          </TabsTrigger>
          <TabsTrigger value="faq" className="text-xs md:text-sm">
            FAQ
          </TabsTrigger>
        </TabsList>

        <TabsContent value="brand">
          <SchoolBrandForm 
            chatbotId={chatbotId}
            onSaveStart={() => {
              setSaving(true);
              setSaveStatus('idle');
            }}
            onSaveComplete={(success) => {
              setSaving(false);
              setSaveStatus(success ? 'success' : 'error');
              
              // Reset status after 3 seconds
              setTimeout(() => {
                setSaveStatus('idle');
              }, 3000);
            }}
          />
        </TabsContent>

        <TabsContent value="contact">
          <SchoolContactForm 
            chatbotId={chatbotId}
            onSaveStart={() => {
              setSaving(true);
              setSaveStatus('idle');
            }}
            onSaveComplete={(success) => {
              setSaving(false);
              setSaveStatus(success ? 'success' : 'error');
              
              // Reset status after 3 seconds
              setTimeout(() => {
                setSaveStatus('idle');
              }, 3000);
            }}
          />
        </TabsContent>

        <TabsContent value="teachers">
          <TeachersForm 
            chatbotId={chatbotId}
            onSaveStart={() => {
              setSaving(true);
              setSaveStatus('idle');
            }}
            onSaveComplete={(success) => {
              setSaving(false);
              setSaveStatus(success ? 'success' : 'error');
              
              // Reset status after 3 seconds
              setTimeout(() => {
                setSaveStatus('idle');
              }, 3000);
            }}
          />
        </TabsContent>

        <TabsContent value="ttc">
          <TTCForm 
            chatbotId={chatbotId}
            onSaveStart={() => {
              setSaving(true);
              setSaveStatus('idle');
            }}
            onSaveComplete={(success) => {
              setSaving(false);
              setSaveStatus(success ? 'success' : 'error');
              
              // Reset status after 3 seconds
              setTimeout(() => {
                setSaveStatus('idle');
              }, 3000);
            }}
          />
        </TabsContent>

        <TabsContent value="retreats">
          <RetreatsForm 
            chatbotId={chatbotId}
            onSaveStart={() => {
              setSaving(true);
              setSaveStatus('idle');
            }}
            onSaveComplete={(success) => {
              setSaving(false);
              setSaveStatus(success ? 'success' : 'error');
              
              // Reset status after 3 seconds
              setTimeout(() => {
                setSaveStatus('idle');
              }, 3000);
            }}
          />
        </TabsContent>

        <TabsContent value="online">
          <OnlineCoursesForm 
            chatbotId={chatbotId}
            onSaveStart={() => {
              setSaving(true);
              setSaveStatus('idle');
            }}
            onSaveComplete={(success) => {
              setSaving(false);
              setSaveStatus(success ? 'success' : 'error');
              
              // Reset status after 3 seconds
              setTimeout(() => {
                setSaveStatus('idle');
              }, 3000);
            }}
          />
        </TabsContent>

        <TabsContent value="facilities">
          <FacilitiesForm 
            chatbotId={chatbotId}
            onSaveStart={() => {
              setSaving(true);
              setSaveStatus('idle');
            }}
            onSaveComplete={(success) => {
              setSaving(false);
              setSaveStatus(success ? 'success' : 'error');
              
              // Reset status after 3 seconds
              setTimeout(() => {
                setSaveStatus('idle');
              }, 3000);
            }}
          />
        </TabsContent>

        <TabsContent value="policies">
          <PoliciesForm 
            chatbotId={chatbotId}
            onSaveStart={() => {
              setSaving(true);
              setSaveStatus('idle');
            }}
            onSaveComplete={(success) => {
              setSaving(false);
              setSaveStatus(success ? 'success' : 'error');
              
              // Reset status after 3 seconds
              setTimeout(() => {
                setSaveStatus('idle');
              }, 3000);
            }}
          />
        </TabsContent>

        <TabsContent value="faq">
          <FAQForm 
            chatbotId={chatbotId}
            onSaveStart={() => {
              setSaving(true);
              setSaveStatus('idle');
            }}
            onSaveComplete={(success) => {
              setSaving(false);
              setSaveStatus(success ? 'success' : 'error');
              
              // Reset status after 3 seconds
              setTimeout(() => {
                setSaveStatus('idle');
              }, 3000);
            }}
          />
        </TabsContent>
      </Tabs>

      {/* Save Status Indicator */}
      {saveStatus !== 'idle' && (
        <div className={`mt-4 p-3 rounded-md ${
          saveStatus === 'success' 
            ? 'bg-green-50 text-green-800' 
            : 'bg-red-50 text-red-800'
        }`}>
          {saveStatus === 'success' 
            ? 'Saved successfully!' 
            : 'Error saving. Please try again.'}
        </div>
      )}

      <div className="mt-6 p-4 bg-yellow-50 rounded-md">
        <h3 className="font-semibold mb-2">How This Works</h3>
        <p>
          Each tab represents a different aspect of your yoga school. Complete as many sections as possible
          for the best results. The AI will use this structured information to provide accurate and detailed
          responses to your visitors' questions.
        </p>
      </div>
    </div>
  );
} 