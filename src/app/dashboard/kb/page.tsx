'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { redirect } from 'next/navigation';
import Link from 'next/link';
import StructuredForm from './structured-form';

interface UserData {
  id: string;
  name: string;
  email: string;
  subscription: {
    status: string;
    currentPeriodEnd: string;
    tokensUsedThisPeriod: number;
    plan: {
      name: string;
      features: {
        kbType: 'simple' | 'structured';
        tokenLimit: number;
        canUseBYOK: boolean;
      };
    };
  };
  chatbot: {
    id: string;
    approvedDomain: string;
    simpleKbText: string | null;
  };
}

export default function KnowledgeBasePage() {
  const { data: session, status } = useSession();
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      redirect('/login');
    }
  }, [status]);

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      if (status !== 'authenticated') return;

      try {
        setLoading(true);
        const response = await fetch('/api/user/profile');
        
        if (!response.ok) {
          throw new Error('Failed to fetch user data');
        }
        
        const data = await response.json();
        setUserData(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching user data:', err);
        setError('Failed to load your profile data');
      } finally {
        setLoading(false);
      }
    };
    
    fetchUserData();
  }, [status]);

  if (status === 'loading' || loading) {
    return <div className="p-8 text-center">Loading...</div>;
  }

  const kbType = userData?.subscription?.plan.features.kbType || 'simple';

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold">Knowledge Base</h1>
        <Link 
          href="/dashboard" 
          className="px-4 py-2 bg-gray-200 hover:bg-gray-300 rounded-md text-gray-800"
        >
          Back to Dashboard
        </Link>
      </div>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Plan Type Information */}
      <div className="bg-blue-50 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-2">Your Knowledge Base Type</h2>
        <p>
          Your subscription plan ({userData?.subscription?.plan.name}) includes the{' '}
          <span className="font-semibold">
            {kbType === 'simple' ? 'Simple Text' : 'Structured Form'}
          </span>{' '}
          knowledge base.
        </p>
      </div>

      {/* Conditional Rendering based on KB Type */}
      {kbType === 'simple' ? (
        <SimpleTextKB initialText={userData?.chatbot?.simpleKbText || ''} chatbotId={userData?.chatbot?.id || ''} />
      ) : (
        <StructuredFormKB chatbotId={userData?.chatbot?.id || ''} />
      )}
    </div>
  );
}

// Simple Text KB Component
function SimpleTextKB({ initialText, chatbotId }: { initialText: string; chatbotId: string }) {
  const [text, setText] = useState(initialText);
  const [saving, setSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleSave = async () => {
    try {
      setSaving(true);
      setSaveStatus('idle');
      
      const response = await fetch('/api/kb/save-simple', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text, chatbotId }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to save knowledge base');
      }
      
      setSaveStatus('success');
    } catch (err) {
      console.error('Error saving knowledge base:', err);
      setSaveStatus('error');
    } finally {
      setSaving(false);
      
      // Reset status after 3 seconds
      setTimeout(() => {
        setSaveStatus('idle');
      }, 3000);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-4">Simple Text Knowledge Base</h2>
      <p className="mb-4 text-gray-600">
        Enter all the information about your yoga school that you want the chatbot to know.
        Include details about your classes, teachers, pricing, location, and any other important information.
      </p>
      
      <div className="mb-4">
        <textarea
          value={text}
          onChange={(e) => setText(e.target.value)}
          className="w-full h-96 p-4 border rounded-md"
          placeholder="Enter your yoga school's information here..."
        />
      </div>
      
      <div className="flex items-center">
        <button
          onClick={handleSave}
          disabled={saving}
          className={`px-4 py-2 rounded-md ${
            saving
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {saving ? 'Saving...' : 'Save Knowledge Base'}
        </button>
        
        {saveStatus === 'success' && (
          <span className="ml-4 text-green-600">Saved successfully!</span>
        )}
        
        {saveStatus === 'error' && (
          <span className="ml-4 text-red-600">Error saving. Please try again.</span>
        )}
      </div>
      
      <div className="mt-6 p-4 bg-yellow-50 rounded-md">
        <h3 className="font-semibold mb-2">How This Works</h3>
        <p>
          When you save this information, our system will automatically process it into chunks
          that the AI can easily reference. The chatbot will use this information to answer
          visitor questions about your yoga school.
        </p>
      </div>
    </div>
  );
}

// Structured Form KB Component
function StructuredFormKB({ chatbotId }: { chatbotId: string }) {
  return <StructuredForm chatbotId={chatbotId} />;
} 