'use client';

import { useState, useEffect } from 'react';

interface TeachersFormProps {
  chatbotId: string;
  onSaveStart: () => void;
  onSaveComplete: (success: boolean) => void;
}

interface Teacher {
  id?: string;
  name: string;
  role: string;
  photoUrl: string;
  bio: string;
  certifications: string[];
}

export default function TeachersForm({
  chatbotId,
  onSaveStart,
  onSaveComplete,
}: TeachersFormProps) {
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingTeacher, setEditingTeacher] = useState<Teacher | null>(null);
  const [isNewTeacher, setIsNewTeacher] = useState(false);
  
  // For certification input
  const [newCertification, setNewCertification] = useState('');

  // Fetch existing data if available
  useEffect(() => {
    const fetchTeachers = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/kb/teachers?chatbotId=${chatbotId}`);
        
        if (response.ok) {
          const data = await response.json();
          if (data.teachers) {
            setTeachers(data.teachers);
          }
        }
      } catch (error) {
        console.error('Error fetching teachers data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTeachers();
  }, [chatbotId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    if (editingTeacher) {
      setEditingTeacher({
        ...editingTeacher,
        [name]: value,
      });
    }
  };

  const addCertification = () => {
    if (newCertification && editingTeacher) {
      setEditingTeacher({
        ...editingTeacher,
        certifications: [...editingTeacher.certifications, newCertification]
      });
      setNewCertification('');
    }
  };

  const removeCertification = (index: number) => {
    if (editingTeacher) {
      const updatedCertifications = [...editingTeacher.certifications];
      updatedCertifications.splice(index, 1);
      setEditingTeacher({
        ...editingTeacher,
        certifications: updatedCertifications
      });
    }
  };

  const startAddingTeacher = () => {
    setEditingTeacher({
      name: '',
      role: '',
      photoUrl: '',
      bio: '',
      certifications: []
    });
    setIsNewTeacher(true);
  };

  const startEditingTeacher = (teacher: Teacher) => {
    setEditingTeacher({ ...teacher });
    setIsNewTeacher(false);
  };

  const cancelEditing = () => {
    setEditingTeacher(null);
  };

  const saveTeacher = () => {
    if (editingTeacher) {
      if (isNewTeacher) {
        // Add new teacher to the list
        setTeachers([...teachers, editingTeacher]);
      } else {
        // Update existing teacher
        setTeachers(teachers.map(t => 
          t.id === editingTeacher.id ? editingTeacher : t
        ));
      }
      setEditingTeacher(null);
    }
  };

  const removeTeacher = (teacherId: string | undefined) => {
    if (teacherId) {
      setTeachers(teachers.filter(t => t.id !== teacherId));
    } else {
      // For new teachers that don't have an ID yet
      const index = teachers.findIndex(t => t === editingTeacher);
      if (index !== -1) {
        const updatedTeachers = [...teachers];
        updatedTeachers.splice(index, 1);
        setTeachers(updatedTeachers);
      }
    }
    setEditingTeacher(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      onSaveStart();
      
      const response = await fetch('/api/kb/save-teachers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chatbotId,
          teachers,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to save teachers data');
      }
      
      onSaveComplete(true);
    } catch (error) {
      console.error('Error saving teachers data:', error);
      onSaveComplete(false);
    }
  };

  if (loading) {
    return <div className="p-4 text-center">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="mb-8">
        <h3 className="text-lg font-medium mb-2">Teachers</h3>
        <p className="text-gray-600 text-sm mb-4">
          Add information about the instructors at your school. Students often choose schools based on the teachers.
        </p>
      </div>

      {/* Teacher List */}
      {!editingTeacher && (
        <>
          <div className="space-y-4">
            {teachers.length > 0 ? (
              teachers.map((teacher, index) => (
                <div key={teacher.id || index} className="p-4 border rounded-md bg-white">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">{teacher.name}</h4>
                      <p className="text-gray-600 text-sm">{teacher.role}</p>
                    </div>
                    <button
                      type="button"
                      onClick={() => startEditingTeacher(teacher)}
                      className="px-3 py-1 bg-blue-100 text-blue-700 rounded-md text-sm"
                    >
                      Edit
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-center text-gray-500 p-4 border-2 border-dashed rounded-md">
                No teachers added yet. Click the button below to add your first teacher.
              </p>
            )}
          </div>

          <div className="flex justify-center">
            <button
              type="button"
              onClick={startAddingTeacher}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
              Add Teacher
            </button>
          </div>
        </>
      )}

      {/* Teacher Edit Form */}
      {editingTeacher && (
        <div className="border rounded-md p-4 bg-gray-50">
          <h4 className="font-medium mb-4">{isNewTeacher ? 'Add New Teacher' : 'Edit Teacher'}</h4>
          
          <div className="space-y-4">
            {/* Teacher Name */}
            <div className="grid gap-2">
              <label htmlFor="name" className="font-medium text-sm">
                Teacher Name <span className="text-red-500">*</span>
              </label>
              <input
                id="name"
                name="name"
                type="text"
                value={editingTeacher.name}
                onChange={handleInputChange}
                required
                className="w-full p-2 border rounded-md"
                placeholder="e.g., John Smith"
              />
            </div>

            {/* Teacher Role */}
            <div className="grid gap-2">
              <label htmlFor="role" className="font-medium text-sm">
                Role
              </label>
              <input
                id="role"
                name="role"
                type="text"
                value={editingTeacher.role}
                onChange={handleInputChange}
                className="w-full p-2 border rounded-md"
                placeholder="e.g., Lead Hatha & Philosophy Teacher"
              />
              <p className="text-xs text-gray-500">The teacher's title or position at your school.</p>
            </div>

            {/* Photo URL */}
            <div className="grid gap-2">
              <label htmlFor="photoUrl" className="font-medium text-sm">
                Photo URL
              </label>
              <input
                id="photoUrl"
                name="photoUrl"
                type="url"
                value={editingTeacher.photoUrl}
                onChange={handleInputChange}
                className="w-full p-2 border rounded-md"
                placeholder="e.g., https://example.com/teacher-photo.jpg"
              />
              <p className="text-xs text-gray-500">URL to a high-quality headshot of the teacher.</p>
            </div>

            {/* Teacher Bio */}
            <div className="grid gap-2">
              <label htmlFor="bio" className="font-medium text-sm">
                Biography
              </label>
              <textarea
                id="bio"
                name="bio"
                value={editingTeacher.bio}
                onChange={handleInputChange}
                rows={5}
                className="w-full p-2 border rounded-md"
                placeholder="e.g., Began their yoga journey at a young age, inspired by..."
              />
              <p className="text-xs text-gray-500">A detailed biography, including their yoga journey and teaching philosophy.</p>
            </div>

            {/* Certifications */}
            <div className="grid gap-2">
              <label className="font-medium text-sm">
                Certifications
              </label>
              
              {/* Add certification input */}
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newCertification}
                  onChange={(e) => setNewCertification(e.target.value)}
                  className="w-full p-2 border rounded-md"
                  placeholder="e.g., E-RYT 500"
                />
                <button
                  type="button"
                  onClick={addCertification}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md"
                  disabled={!newCertification}
                >
                  Add
                </button>
              </div>

              {/* List of certifications */}
              {editingTeacher.certifications.length > 0 ? (
                <div className="flex flex-wrap gap-2 mt-2">
                  {editingTeacher.certifications.map((cert, index) => (
                    <div key={index} className="flex items-center bg-blue-50 px-3 py-1 rounded-full">
                      <span className="text-sm">{cert}</span>
                      <button
                        type="button"
                        onClick={() => removeCertification(index)}
                        className="ml-2 text-red-500 hover:text-red-700"
                      >
                        &times;
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 italic">No certifications added yet.</p>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-2 pt-4">
              <button
                type="button"
                onClick={cancelEditing}
                className="px-4 py-2 border border-gray-300 rounded-md"
              >
                Cancel
              </button>
              {!isNewTeacher && (
                <button
                  type="button"
                  onClick={() => removeTeacher(editingTeacher.id)}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md"
                >
                  Delete
                </button>
              )}
              <button
                type="button"
                onClick={saveTeacher}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
              >
                Save Teacher
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Submit Button - only show when not editing a teacher */}
      {!editingTeacher && teachers.length > 0 && (
        <form onSubmit={handleSubmit} className="pt-6">
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
          >
            Save All Teachers
          </button>
        </form>
      )}
    </div>
  );
} 