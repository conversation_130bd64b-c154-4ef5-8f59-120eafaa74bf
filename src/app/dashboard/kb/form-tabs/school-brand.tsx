'use client';

import { useState, useEffect } from 'react';

interface SchoolBrandFormProps {
  chatbotId: string;
  onSaveStart: () => void;
  onSaveComplete: (success: boolean) => void;
}

interface SchoolBrandData {
  schoolName: string;
  tagline: string;
  schoolType: string;
  yogaStylesTaught: string[];
  missionStatement: string;
  aboutTheSchool: string;
  founderInfo: string;
}

export default function SchoolBrandForm({
  chatbotId,
  onSaveStart,
  onSaveComplete,
}: SchoolBrandFormProps) {
  const [formData, setFormData] = useState<SchoolBrandData>({
    schoolName: '',
    tagline: '',
    schoolType: '',
    yogaStylesTaught: [],
    missionStatement: '',
    aboutTheSchool: '',
    founderInfo: '',
  });

  const [loading, setLoading] = useState(true);

  // Fetch existing data if available
  useEffect(() => {
    const fetchBrandData = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/kb/brand?chatbotId=${chatbotId}`);
        
        if (response.ok) {
          const data = await response.json();
          if (data.brand) {
            setFormData(data.brand);
          }
        }
      } catch (error) {
        console.error('Error fetching brand data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchBrandData();
  }, [chatbotId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleYogaStyleChange = (style: string) => {
    setFormData((prev) => {
      const currentStyles = [...prev.yogaStylesTaught];
      
      if (currentStyles.includes(style)) {
        return {
          ...prev,
          yogaStylesTaught: currentStyles.filter((s) => s !== style),
        };
      } else {
        return {
          ...prev,
          yogaStylesTaught: [...currentStyles, style],
        };
      }
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      onSaveStart();
      
      const response = await fetch('/api/kb/save-brand', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chatbotId,
          brand: formData,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to save brand data');
      }
      
      onSaveComplete(true);
    } catch (error) {
      console.error('Error saving brand data:', error);
      onSaveComplete(false);
    }
  };

  if (loading) {
    return <div className="p-4 text-center">Loading...</div>;
  }

  // Common yoga styles for checkboxes
  const yogaStyles = [
    'Hatha', 'Vinyasa', 'Ashtanga', 'Iyengar', 'Kundalini', 
    'Yin', 'Restorative', 'Power', 'Bikram', 'Hot', 'Prenatal',
    'Meditation', 'Pranayama', 'Yoga Nidra', 'Aerial', 'Acro'
  ];

  // School types for dropdown
  const schoolTypes = [
    'Ashram', 'Boutique Studio', 'Yoga Center', 'Retreat Center',
    'Wellness Center', 'Gym', 'Online Platform', 'Mobile Studio', 'Other'
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="mb-8">
        <h3 className="text-lg font-medium mb-2">School Identity & Brand</h3>
        <p className="text-gray-600 text-sm mb-4">
          This section captures your school's core brand, mission, and unique selling propositions.
        </p>
      </div>

      {/* School Name */}
      <div className="grid gap-2">
        <label htmlFor="schoolName" className="font-medium text-sm">
          School Name <span className="text-red-500">*</span>
        </label>
        <input
          id="schoolName"
          name="schoolName"
          type="text"
          value={formData.schoolName}
          onChange={handleInputChange}
          required
          className="w-full p-2 border rounded-md"
          placeholder="e.g., Ananda Yoga School"
        />
      </div>

      {/* Tagline */}
      <div className="grid gap-2">
        <label htmlFor="tagline" className="font-medium text-sm">
          Tagline
        </label>
        <input
          id="tagline"
          name="tagline"
          type="text"
          value={formData.tagline}
          onChange={handleInputChange}
          className="w-full p-2 border rounded-md"
          placeholder="e.g., Authentic Yoga on the Banks of the Ganges"
        />
        <p className="text-xs text-gray-500">A short, memorable slogan that captures your essence.</p>
      </div>

      {/* School Type */}
      <div className="grid gap-2">
        <label htmlFor="schoolType" className="font-medium text-sm">
          School Type
        </label>
        <select
          id="schoolType"
          name="schoolType"
          value={formData.schoolType}
          onChange={handleInputChange}
          className="w-full p-2 border rounded-md"
        >
          <option value="">Select a type...</option>
          {schoolTypes.map((type) => (
            <option key={type} value={type}>
              {type}
            </option>
          ))}
        </select>
      </div>

      {/* Yoga Styles Taught */}
      <div className="grid gap-2">
        <label className="font-medium text-sm">Yoga Styles Taught</label>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
          {yogaStyles.map((style) => (
            <div key={style} className="flex items-center">
              <input
                type="checkbox"
                id={`style-${style}`}
                checked={formData.yogaStylesTaught.includes(style)}
                onChange={() => handleYogaStyleChange(style)}
                className="mr-2"
              />
              <label htmlFor={`style-${style}`} className="text-sm">
                {style}
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Mission Statement */}
      <div className="grid gap-2">
        <label htmlFor="missionStatement" className="font-medium text-sm">
          Mission Statement
        </label>
        <textarea
          id="missionStatement"
          name="missionStatement"
          value={formData.missionStatement}
          onChange={handleInputChange}
          rows={3}
          className="w-full p-2 border rounded-md"
          placeholder="e.g., To preserve and share the ancient wisdom of classical yoga..."
        />
        <p className="text-xs text-gray-500">Your school's core purpose or philosophy.</p>
      </div>

      {/* About The School */}
      <div className="grid gap-2">
        <label htmlFor="aboutTheSchool" className="font-medium text-sm">
          About The School
        </label>
        <textarea
          id="aboutTheSchool"
          name="aboutTheSchool"
          value={formData.aboutTheSchool}
          onChange={handleInputChange}
          rows={5}
          className="w-full p-2 border rounded-md"
          placeholder="e.g., Established in 1998, our school is dedicated to the traditional path..."
        />
        <p className="text-xs text-gray-500">A detailed description of your school's history, lineage, and approach.</p>
      </div>

      {/* Founder Info */}
      <div className="grid gap-2">
        <label htmlFor="founderInfo" className="font-medium text-sm">
          Founder Information
        </label>
        <textarea
          id="founderInfo"
          name="founderInfo"
          value={formData.founderInfo}
          onChange={handleInputChange}
          rows={4}
          className="w-full p-2 border rounded-md"
          placeholder="e.g., Our founder, a lifelong yogi, studied for decades..."
        />
        <p className="text-xs text-gray-500">A brief biography of the founder(s).</p>
      </div>

      {/* Submit Button */}
      <div className="pt-4">
        <button
          type="submit"
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
        >
          Save School Brand Information
        </button>
      </div>
    </form>
  );
} 