'use client';

import { useState, useEffect } from 'react';

interface PoliciesFormProps {
  chatbotId: string;
  onSaveStart: () => void;
  onSaveComplete: (success: boolean) => void;
}

interface Policy {
  codeOfConduct: string;
  paymentPolicy: string;
  cancellationAndRefundPolicy: string;
  visaInformation: string;
  whatToBring: string;
}

export default function PoliciesForm({
  chatbotId,
  onSaveStart,
  onSaveComplete,
}: PoliciesFormProps) {
  const [policy, setPolicy] = useState<Policy>({
    codeOfConduct: '',
    paymentPolicy: '',
    cancellationAndRefundPolicy: '',
    visaInformation: '',
    whatToBring: '',
  });
  
  const [loading, setLoading] = useState(true);

  // Fetch existing data if available
  useEffect(() => {
    const fetchPolicy = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/kb/policy?chatbotId=${chatbotId}`);
        
        if (response.ok) {
          const data = await response.json();
          if (data.policy) {
            setPolicy({
              codeOfConduct: data.policy.codeOfConduct || '',
              paymentPolicy: data.policy.paymentPolicy || '',
              cancellationAndRefundPolicy: data.policy.cancellationAndRefundPolicy || '',
              visaInformation: data.policy.visaInformation || '',
              whatToBring: data.policy.whatToBring || '',
            });
          }
        }
      } catch (error) {
        console.error('Error fetching policy data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPolicy();
  }, [chatbotId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setPolicy({
      ...policy,
      [name]: value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      onSaveStart();
      
      const response = await fetch('/api/kb/save-policy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chatbotId,
          policy,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to save policy data');
      }
      
      onSaveComplete(true);
    } catch (error) {
      console.error('Error saving policy data:', error);
      onSaveComplete(false);
    }
  };

  if (loading) {
    return <div className="p-4 text-center">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="mb-8">
        <h3 className="text-lg font-medium mb-2">Policies & Practicalities</h3>
        <p className="text-gray-600 text-sm mb-4">
          Add information about your school's policies, rules, and practical information for students.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Code of Conduct */}
        <div className="grid gap-2">
          <label htmlFor="codeOfConduct" className="font-medium text-sm">
            Code of Conduct
          </label>
          <textarea
            id="codeOfConduct"
            name="codeOfConduct"
            value={policy.codeOfConduct}
            onChange={handleInputChange}
            rows={5}
            className="w-full p-2 border rounded-md"
            placeholder="e.g., Students are expected to attend all classes punctually..."
          />
          <p className="text-xs text-gray-500">
            Describe the rules and etiquette that students are expected to follow during their stay.
          </p>
        </div>

        {/* Payment Policy */}
        <div className="grid gap-2">
          <label htmlFor="paymentPolicy" className="font-medium text-sm">
            Payment Policy
          </label>
          <textarea
            id="paymentPolicy"
            name="paymentPolicy"
            value={policy.paymentPolicy}
            onChange={handleInputChange}
            rows={5}
            className="w-full p-2 border rounded-md"
            placeholder="e.g., A non-refundable deposit is required to book..."
          />
          <p className="text-xs text-gray-500">
            Explain your payment terms, deposit requirements, and accepted payment methods.
          </p>
        </div>

        {/* Cancellation and Refund Policy */}
        <div className="grid gap-2">
          <label htmlFor="cancellationAndRefundPolicy" className="font-medium text-sm">
            Cancellation and Refund Policy
          </label>
          <textarea
            id="cancellationAndRefundPolicy"
            name="cancellationAndRefundPolicy"
            value={policy.cancellationAndRefundPolicy}
            onChange={handleInputChange}
            rows={5}
            className="w-full p-2 border rounded-md"
            placeholder="e.g., The deposit is non-refundable. If you cancel more than 30 days..."
          />
          <p className="text-xs text-gray-500">
            Clearly outline your cancellation policy and refund terms for different scenarios.
          </p>
        </div>

        {/* Visa Information */}
        <div className="grid gap-2">
          <label htmlFor="visaInformation" className="font-medium text-sm">
            Visa Information
          </label>
          <textarea
            id="visaInformation"
            name="visaInformation"
            value={policy.visaInformation}
            onChange={handleInputChange}
            rows={5}
            className="w-full p-2 border rounded-md"
            placeholder="e.g., It is your responsibility to secure the appropriate visa..."
          />
          <p className="text-xs text-gray-500">
            Provide guidance for international students regarding visa requirements and processes.
          </p>
        </div>

        {/* What to Bring */}
        <div className="grid gap-2">
          <label htmlFor="whatToBring" className="font-medium text-sm">
            What to Bring
          </label>
          <textarea
            id="whatToBring"
            name="whatToBring"
            value={policy.whatToBring}
            onChange={handleInputChange}
            rows={5}
            className="w-full p-2 border rounded-md"
            placeholder="e.g., Comfortable yoga clothes, a reusable water bottle, a journal..."
          />
          <p className="text-xs text-gray-500">
            Provide a recommended packing list for students attending your programs.
          </p>
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
          >
            Save Policies
          </button>
        </div>
      </form>
    </div>
  );
}
