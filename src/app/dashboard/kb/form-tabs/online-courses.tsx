'use client';

import { useState, useEffect } from 'react';

interface OnlineCoursesFormProps {
  chatbotId: string;
  onSaveStart: () => void;
  onSaveComplete: (success: boolean) => void;
}

interface OnlineCourse {
  id?: string;
  name: string;
  format: string;
  contentCovered: string;
  instructorName: string;
  accessType: string;
  price: number;
  currency: string;
  accessLink: string;
}

export default function OnlineCoursesForm({
  chatbotId,
  onSaveStart,
  onSaveComplete,
}: OnlineCoursesFormProps) {
  const [onlineCourses, setOnlineCourses] = useState<OnlineCourse[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingCourse, setEditingCourse] = useState<OnlineCourse | null>(null);
  const [isNewCourse, setIsNewCourse] = useState(false);

  // Fetch existing data if available
  useEffect(() => {
    const fetchOnlineCourses = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/kb/online-course?chatbotId=${chatbotId}`);
        
        if (response.ok) {
          const data = await response.json();
          if (data.onlineCourses) {
            setOnlineCourses(data.onlineCourses);
          }
        }
      } catch (error) {
        console.error('Error fetching online course data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchOnlineCourses();
  }, [chatbotId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    if (editingCourse) {
      setEditingCourse({
        ...editingCourse,
        [name]: name === 'price' ? parseFloat(value) : value,
      });
    }
  };

  const startAddingCourse = () => {
    setEditingCourse({
      name: '',
      format: '',
      contentCovered: '',
      instructorName: '',
      accessType: 'Lifetime Access',
      price: 0,
      currency: 'USD',
      accessLink: '',
    });
    setIsNewCourse(true);
  };

  const startEditingCourse = (course: OnlineCourse) => {
    setEditingCourse({ ...course });
    setIsNewCourse(false);
  };

  const cancelEditing = () => {
    setEditingCourse(null);
  };

  const saveCourse = () => {
    if (editingCourse) {
      if (isNewCourse) {
        // Add new course to the list
        setOnlineCourses([...onlineCourses, editingCourse]);
      } else {
        // Update existing course
        setOnlineCourses(onlineCourses.map(c => 
          c.id === editingCourse.id ? editingCourse : c
        ));
      }
      setEditingCourse(null);
    }
  };

  const removeCourse = (courseId: string | undefined) => {
    if (courseId) {
      setOnlineCourses(onlineCourses.filter(c => c.id !== courseId));
    } else {
      // For new courses that don't have an ID yet
      const index = onlineCourses.findIndex(c => c === editingCourse);
      if (index !== -1) {
        const updatedCourses = [...onlineCourses];
        updatedCourses.splice(index, 1);
        setOnlineCourses(updatedCourses);
      }
    }
    setEditingCourse(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      onSaveStart();
      
      const response = await fetch('/api/kb/save-online-course', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chatbotId,
          onlineCourses,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to save online course data');
      }
      
      onSaveComplete(true);
    } catch (error) {
      console.error('Error saving online course data:', error);
      onSaveComplete(false);
    }
  };

  if (loading) {
    return <div className="p-4 text-center">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="mb-8">
        <h3 className="text-lg font-medium mb-2">Online Courses & Offerings</h3>
        <p className="text-gray-600 text-sm mb-4">
          Add information about your online courses, workshops, and digital offerings.
        </p>
      </div>

      {/* Online Course List */}
      {!editingCourse && (
        <>
          <div className="space-y-4">
            {onlineCourses.length > 0 ? (
              onlineCourses.map((course, index) => (
                <div key={course.id || index} className="p-4 border rounded-md bg-white">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">{course.name}</h4>
                      <p className="text-gray-600 text-sm">{course.format} • {course.price} {course.currency}</p>
                    </div>
                    <button
                      type="button"
                      onClick={() => startEditingCourse(course)}
                      className="px-3 py-1 bg-blue-100 text-blue-700 rounded-md text-sm"
                    >
                      Edit
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-center text-gray-500 p-4 border-2 border-dashed rounded-md">
                No online courses added yet. Click the button below to add your first online course.
              </p>
            )}
          </div>

          <div className="flex justify-center">
            <button
              type="button"
              onClick={startAddingCourse}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
              Add Online Course
            </button>
          </div>
        </>
      )}

      {/* Online Course Edit Form */}
      {editingCourse && (
        <div className="border rounded-md p-4 bg-gray-50">
          <h4 className="font-medium mb-4">{isNewCourse ? 'Add New Online Course' : 'Edit Online Course'}</h4>
          
          <div className="space-y-4">
            {/* Course Name */}
            <div className="grid gap-2">
              <label htmlFor="name" className="font-medium text-sm">
                Course Name <span className="text-red-500">*</span>
              </label>
              <input
                id="name"
                name="name"
                type="text"
                value={editingCourse.name}
                onChange={handleInputChange}
                required
                className="w-full p-2 border rounded-md"
                placeholder="e.g., Mastering Pranayama: A 4-Week Online Immersion"
              />
            </div>

            {/* Format */}
            <div className="grid gap-2">
              <label htmlFor="format" className="font-medium text-sm">
                Format
              </label>
              <input
                id="format"
                name="format"
                type="text"
                value={editingCourse.format}
                onChange={handleInputChange}
                className="w-full p-2 border rounded-md"
                placeholder="e.g., Live online sessions via Zoom + lifetime access to recordings"
              />
            </div>

            {/* Content Covered */}
            <div className="grid gap-2">
              <label htmlFor="contentCovered" className="font-medium text-sm">
                Content Covered
              </label>
              <textarea
                id="contentCovered"
                name="contentCovered"
                value={editingCourse.contentCovered}
                onChange={handleInputChange}
                rows={3}
                className="w-full p-2 border rounded-md"
                placeholder="e.g., The science of breath, key pranayama techniques..."
              />
            </div>

            {/* Instructor Name */}
            <div className="grid gap-2">
              <label htmlFor="instructorName" className="font-medium text-sm">
                Instructor Name
              </label>
              <input
                id="instructorName"
                name="instructorName"
                type="text"
                value={editingCourse.instructorName}
                onChange={handleInputChange}
                className="w-full p-2 border rounded-md"
                placeholder="e.g., John Smith"
              />
            </div>

            {/* Access Type */}
            <div className="grid gap-2">
              <label htmlFor="accessType" className="font-medium text-sm">
                Access Type
              </label>
              <select
                id="accessType"
                name="accessType"
                value={editingCourse.accessType}
                onChange={handleInputChange}
                className="w-full p-2 border rounded-md"
              >
                <option value="Lifetime Access">Lifetime Access</option>
                <option value="1 Month">1 Month</option>
                <option value="3 Months">3 Months</option>
                <option value="6 Months">6 Months</option>
                <option value="1 Year">1 Year</option>
                <option value="Subscription">Subscription</option>
              </select>
            </div>

            {/* Price and Currency */}
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <label htmlFor="price" className="font-medium text-sm">
                  Price
                </label>
                <input
                  id="price"
                  name="price"
                  type="number"
                  value={editingCourse.price || ''}
                  onChange={handleInputChange}
                  className="w-full p-2 border rounded-md"
                  placeholder="e.g., 199"
                  min="0"
                  step="0.01"
                />
              </div>
              <div className="grid gap-2">
                <label htmlFor="currency" className="font-medium text-sm">
                  Currency
                </label>
                <select
                  id="currency"
                  name="currency"
                  value={editingCourse.currency}
                  onChange={handleInputChange}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="USD">USD</option>
                  <option value="EUR">EUR</option>
                  <option value="GBP">GBP</option>
                  <option value="INR">INR</option>
                </select>
              </div>
            </div>

            {/* Access Link */}
            <div className="grid gap-2">
              <label htmlFor="accessLink" className="font-medium text-sm">
                Access Link
              </label>
              <input
                id="accessLink"
                name="accessLink"
                type="url"
                value={editingCourse.accessLink}
                onChange={handleInputChange}
                className="w-full p-2 border rounded-md"
                placeholder="e.g., https://school.podia.com/pranayama"
              />
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-2 pt-4">
              <button
                type="button"
                onClick={cancelEditing}
                className="px-4 py-2 border border-gray-300 rounded-md"
              >
                Cancel
              </button>
              {!isNewCourse && (
                <button
                  type="button"
                  onClick={() => removeCourse(editingCourse.id)}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md"
                >
                  Delete
                </button>
              )}
              <button
                type="button"
                onClick={saveCourse}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
              >
                Save Course
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Submit Button - only show when not editing a course */}
      {!editingCourse && onlineCourses.length > 0 && (
        <form onSubmit={handleSubmit} className="pt-6">
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
          >
            Save All Courses
          </button>
        </form>
      )}
    </div>
  );
}
