'use client';

import { useState, useEffect } from 'react';

interface TTCFormProps {
  chatbotId: string;
  onSaveStart: () => void;
  onSaveComplete: (success: boolean) => void;
}

interface PriceOption {
  type: string;
  price: number;
  currency: string;
}

interface DateOption {
  start: string;
  end: string;
  status: string;
}

interface TTC {
  id?: string;
  name: string;
  certificationBody: string;
  summary: string;
  duration: string;
  skillLevel: string;
  curriculumDetails: string;
  sampleDailySchedule: string;
  priceOptions: PriceOption[];
  inclusions: string[];
  exclusions: string[];
  upcomingDates: DateOption[];
  applicationProcess: string;
}

export default function TTCForm({
  chatbotId,
  onSaveStart,
  onSaveComplete,
}: TTCFormProps) {
  const [ttcs, setTTCs] = useState<TTC[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingTTC, setEditingTTC] = useState<TTC | null>(null);
  const [isNewTTC, setIsNewTTC] = useState(false);
  
  // For list inputs
  const [newInclusion, setNewInclusion] = useState('');
  const [newExclusion, setNewExclusion] = useState('');
  
  // For price options
  const [newPriceOption, setNewPriceOption] = useState<PriceOption>({
    type: '',
    price: 0,
    currency: 'USD'
  });
  
  // For date options
  const [newDateOption, setNewDateOption] = useState<DateOption>({
    start: '',
    end: '',
    status: 'Open'
  });

  // Fetch existing data if available
  useEffect(() => {
    const fetchTTCs = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/kb/ttc?chatbotId=${chatbotId}`);
        
        if (response.ok) {
          const data = await response.json();
          if (data.ttcs) {
            setTTCs(data.ttcs);
          }
        }
      } catch (error) {
        console.error('Error fetching TTC data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTTCs();
  }, [chatbotId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    if (editingTTC) {
      setEditingTTC({
        ...editingTTC,
        [name]: value,
      });
    }
  };

  // Price option handlers
  const handlePriceOptionChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewPriceOption({
      ...newPriceOption,
      [name]: name === 'price' ? parseFloat(value) : value,
    });
  };

  const addPriceOption = () => {
    if (newPriceOption.type && newPriceOption.price > 0 && editingTTC) {
      setEditingTTC({
        ...editingTTC,
        priceOptions: [...editingTTC.priceOptions, { ...newPriceOption }]
      });
      setNewPriceOption({
        type: '',
        price: 0,
        currency: 'USD'
      });
    }
  };

  const removePriceOption = (index: number) => {
    if (editingTTC) {
      const updatedPriceOptions = [...editingTTC.priceOptions];
      updatedPriceOptions.splice(index, 1);
      setEditingTTC({
        ...editingTTC,
        priceOptions: updatedPriceOptions
      });
    }
  };

  // Date option handlers
  const handleDateOptionChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewDateOption({
      ...newDateOption,
      [name]: value,
    });
  };

  const addDateOption = () => {
    if (newDateOption.start && newDateOption.end && editingTTC) {
      setEditingTTC({
        ...editingTTC,
        upcomingDates: [...editingTTC.upcomingDates, { ...newDateOption }]
      });
      setNewDateOption({
        start: '',
        end: '',
        status: 'Open'
      });
    }
  };

  const removeDateOption = (index: number) => {
    if (editingTTC) {
      const updatedDateOptions = [...editingTTC.upcomingDates];
      updatedDateOptions.splice(index, 1);
      setEditingTTC({
        ...editingTTC,
        upcomingDates: updatedDateOptions
      });
    }
  };

  // Inclusion/exclusion handlers
  const addInclusion = () => {
    if (newInclusion && editingTTC) {
      setEditingTTC({
        ...editingTTC,
        inclusions: [...editingTTC.inclusions, newInclusion]
      });
      setNewInclusion('');
    }
  };

  const removeInclusion = (index: number) => {
    if (editingTTC) {
      const updatedInclusions = [...editingTTC.inclusions];
      updatedInclusions.splice(index, 1);
      setEditingTTC({
        ...editingTTC,
        inclusions: updatedInclusions
      });
    }
  };

  const addExclusion = () => {
    if (newExclusion && editingTTC) {
      setEditingTTC({
        ...editingTTC,
        exclusions: [...editingTTC.exclusions, newExclusion]
      });
      setNewExclusion('');
    }
  };

  const removeExclusion = (index: number) => {
    if (editingTTC) {
      const updatedExclusions = [...editingTTC.exclusions];
      updatedExclusions.splice(index, 1);
      setEditingTTC({
        ...editingTTC,
        exclusions: updatedExclusions
      });
    }
  };

  const startAddingTTC = () => {
    setEditingTTC({
      name: '',
      certificationBody: '',
      summary: '',
      duration: '',
      skillLevel: '',
      curriculumDetails: '',
      sampleDailySchedule: '',
      priceOptions: [],
      inclusions: [],
      exclusions: [],
      upcomingDates: [],
      applicationProcess: ''
    });
    setIsNewTTC(true);
  };

  const startEditingTTC = (ttc: TTC) => {
    setEditingTTC({ ...ttc });
    setIsNewTTC(false);
  };

  const cancelEditing = () => {
    setEditingTTC(null);
  };

  const saveTTC = () => {
    if (editingTTC) {
      if (isNewTTC) {
        // Add new TTC to the list
        setTTCs([...ttcs, editingTTC]);
      } else {
        // Update existing TTC
        setTTCs(ttcs.map(t => 
          t.id === editingTTC.id ? editingTTC : t
        ));
      }
      setEditingTTC(null);
    }
  };

  const removeTTC = (ttcId: string | undefined) => {
    if (ttcId) {
      setTTCs(ttcs.filter(t => t.id !== ttcId));
    } else {
      // For new TTCs that don't have an ID yet
      const index = ttcs.findIndex(t => t === editingTTC);
      if (index !== -1) {
        const updatedTTCs = [...ttcs];
        updatedTTCs.splice(index, 1);
        setTTCs(updatedTTCs);
      }
    }
    setEditingTTC(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      onSaveStart();
      
      const response = await fetch('/api/kb/save-ttc', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chatbotId,
          ttcs,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to save TTC data');
      }
      
      onSaveComplete(true);
    } catch (error) {
      console.error('Error saving TTC data:', error);
      onSaveComplete(false);
    }
  };

  if (loading) {
    return <div className="p-4 text-center">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="mb-8">
        <h3 className="text-lg font-medium mb-2">Teacher Training Courses (TTC)</h3>
        <p className="text-gray-600 text-sm mb-4">
          Add information about your yoga teacher training courses. This section is for residential, certification-based courses.
        </p>
      </div>

      {/* TTC List */}
      {!editingTTC && (
        <>
          <div className="space-y-4">
            {ttcs.length > 0 ? (
              ttcs.map((ttc, index) => (
                <div key={ttc.id || index} className="p-4 border rounded-md bg-white">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">{ttc.name}</h4>
                      <p className="text-gray-600 text-sm">{ttc.certificationBody} • {ttc.duration}</p>
                    </div>
                    <button
                      type="button"
                      onClick={() => startEditingTTC(ttc)}
                      className="px-3 py-1 bg-blue-100 text-blue-700 rounded-md text-sm"
                    >
                      Edit
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-center text-gray-500 p-4 border-2 border-dashed rounded-md">
                No teacher training courses added yet. Click the button below to add your first course.
              </p>
            )}
          </div>

          <div className="flex justify-center">
            <button
              type="button"
              onClick={startAddingTTC}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
              Add Course
            </button>
          </div>
        </>
      )}

      {/* TTC Edit Form */}
      {editingTTC && (
        <div className="border rounded-md p-4 bg-gray-50">
          <h4 className="font-medium mb-4">{isNewTTC ? 'Add New Teacher Training Course' : 'Edit Teacher Training Course'}</h4>
          
          <div className="space-y-4">
            {/* Course Name */}
            <div className="grid gap-2">
              <label htmlFor="name" className="font-medium text-sm">
                Course Name <span className="text-red-500">*</span>
              </label>
              <input
                id="name"
                name="name"
                type="text"
                value={editingTTC.name}
                onChange={handleInputChange}
                required
                className="w-full p-2 border rounded-md"
                placeholder="e.g., 200-Hour Foundational Hatha Yoga TTC"
              />
            </div>

            {/* Certification Body */}
            <div className="grid gap-2">
              <label htmlFor="certificationBody" className="font-medium text-sm">
                Certification Body
              </label>
              <input
                id="certificationBody"
                name="certificationBody"
                type="text"
                value={editingTTC.certificationBody}
                onChange={handleInputChange}
                className="w-full p-2 border rounded-md"
                placeholder="e.g., Yoga Alliance (USA)"
              />
            </div>

            {/* Course Summary */}
            <div className="grid gap-2">
              <label htmlFor="summary" className="font-medium text-sm">
                Course Summary
              </label>
              <textarea
                id="summary"
                name="summary"
                value={editingTTC.summary}
                onChange={handleInputChange}
                rows={3}
                className="w-full p-2 border rounded-md"
                placeholder="e.g., Our foundational 200-hour TTC provides a comprehensive introduction..."
              />
            </div>

            {/* Course Duration */}
            <div className="grid gap-2">
              <label htmlFor="duration" className="font-medium text-sm">
                Course Duration
              </label>
              <input
                id="duration"
                name="duration"
                type="text"
                value={editingTTC.duration}
                onChange={handleInputChange}
                className="w-full p-2 border rounded-md"
                placeholder="e.g., 28 Days, 27 Nights"
              />
            </div>

            {/* Skill Level */}
            <div className="grid gap-2">
              <label htmlFor="skillLevel" className="font-medium text-sm">
                Skill Level
              </label>
              <input
                id="skillLevel"
                name="skillLevel"
                type="text"
                value={editingTTC.skillLevel}
                onChange={handleInputChange}
                className="w-full p-2 border rounded-md"
                placeholder="e.g., Beginner to Intermediate"
              />
            </div>

            {/* Curriculum Details */}
            <div className="grid gap-2">
              <label htmlFor="curriculumDetails" className="font-medium text-sm">
                Curriculum Details
              </label>
              <textarea
                id="curriculumDetails"
                name="curriculumDetails"
                value={editingTTC.curriculumDetails}
                onChange={handleInputChange}
                rows={4}
                className="w-full p-2 border rounded-md"
                placeholder="e.g., Asana Alignment, Pranayama, Yoga Philosophy, Anatomy..."
              />
            </div>

            {/* Sample Daily Schedule */}
            <div className="grid gap-2">
              <label htmlFor="sampleDailySchedule" className="font-medium text-sm">
                Sample Daily Schedule
              </label>
              <textarea
                id="sampleDailySchedule"
                name="sampleDailySchedule"
                value={editingTTC.sampleDailySchedule}
                onChange={handleInputChange}
                rows={4}
                className="w-full p-2 border rounded-md"
                placeholder="e.g., 5:30 AM: Meditation, 6:30 AM: Asana Practice, 8:30 AM: Breakfast..."
              />
            </div>

            {/* Price Options */}
            <div className="grid gap-2">
              <label className="font-medium text-sm">
                Price Options
              </label>
              
              <div className="flex flex-wrap gap-2">
                <input
                  type="text"
                  name="type"
                  value={newPriceOption.type}
                  onChange={handlePriceOptionChange}
                  className="flex-1 p-2 border rounded-md"
                  placeholder="Room Type (e.g., Private Room)"
                />
                <input
                  type="number"
                  name="price"
                  value={newPriceOption.price || ''}
                  onChange={handlePriceOptionChange}
                  className="w-24 p-2 border rounded-md"
                  placeholder="Price"
                  min="0"
                />
                <select
                  name="currency"
                  value={newPriceOption.currency}
                  onChange={handlePriceOptionChange}
                  className="w-24 p-2 border rounded-md"
                >
                  <option value="USD">USD</option>
                  <option value="EUR">EUR</option>
                  <option value="GBP">GBP</option>
                  <option value="INR">INR</option>
                </select>
                <button
                  type="button"
                  onClick={addPriceOption}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md"
                  disabled={!newPriceOption.type || newPriceOption.price <= 0}
                >
                  Add
                </button>
              </div>

              {/* List of price options */}
              {editingTTC.priceOptions.length > 0 ? (
                <div className="space-y-2 mt-2">
                  {editingTTC.priceOptions.map((option, index) => (
                    <div key={index} className="flex justify-between items-center bg-blue-50 p-2 rounded-md">
                      <span>
                        {option.type}: {option.price} {option.currency}
                      </span>
                      <button
                        type="button"
                        onClick={() => removePriceOption(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        &times;
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 italic">No price options added yet.</p>
              )}
            </div>

            {/* Inclusions */}
            <div className="grid gap-2">
              <label className="font-medium text-sm">
                Inclusions
              </label>
              
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newInclusion}
                  onChange={(e) => setNewInclusion(e.target.value)}
                  className="w-full p-2 border rounded-md"
                  placeholder="e.g., Private room with AC"
                />
                <button
                  type="button"
                  onClick={addInclusion}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md"
                  disabled={!newInclusion}
                >
                  Add
                </button>
              </div>

              {/* List of inclusions */}
              {editingTTC.inclusions.length > 0 ? (
                <div className="space-y-2 mt-2">
                  {editingTTC.inclusions.map((item, index) => (
                    <div key={index} className="flex justify-between items-center bg-green-50 p-2 rounded-md">
                      <span>{item}</span>
                      <button
                        type="button"
                        onClick={() => removeInclusion(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        &times;
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 italic">No inclusions added yet.</p>
              )}
            </div>

            {/* Exclusions */}
            <div className="grid gap-2">
              <label className="font-medium text-sm">
                Exclusions
              </label>
              
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newExclusion}
                  onChange={(e) => setNewExclusion(e.target.value)}
                  className="w-full p-2 border rounded-md"
                  placeholder="e.g., Airfare, Visa fees"
                />
                <button
                  type="button"
                  onClick={addExclusion}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md"
                  disabled={!newExclusion}
                >
                  Add
                </button>
              </div>

              {/* List of exclusions */}
              {editingTTC.exclusions.length > 0 ? (
                <div className="space-y-2 mt-2">
                  {editingTTC.exclusions.map((item, index) => (
                    <div key={index} className="flex justify-between items-center bg-red-50 p-2 rounded-md">
                      <span>{item}</span>
                      <button
                        type="button"
                        onClick={() => removeExclusion(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        &times;
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 italic">No exclusions added yet.</p>
              )}
            </div>

            {/* Upcoming Dates */}
            <div className="grid gap-2">
              <label className="font-medium text-sm">
                Upcoming Dates
              </label>
              
              <div className="flex flex-wrap gap-2">
                <input
                  type="date"
                  name="start"
                  value={newDateOption.start}
                  onChange={handleDateOptionChange}
                  className="flex-1 p-2 border rounded-md"
                  placeholder="Start Date"
                />
                <input
                  type="date"
                  name="end"
                  value={newDateOption.end}
                  onChange={handleDateOptionChange}
                  className="flex-1 p-2 border rounded-md"
                  placeholder="End Date"
                />
                <select
                  name="status"
                  value={newDateOption.status}
                  onChange={handleDateOptionChange}
                  className="w-32 p-2 border rounded-md"
                >
                  <option value="Open">Open</option>
                  <option value="Filling Fast">Filling Fast</option>
                  <option value="Almost Full">Almost Full</option>
                  <option value="Full">Full</option>
                  <option value="Waitlist">Waitlist</option>
                </select>
                <button
                  type="button"
                  onClick={addDateOption}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md"
                  disabled={!newDateOption.start || !newDateOption.end}
                >
                  Add
                </button>
              </div>

              {/* List of date options */}
              {editingTTC.upcomingDates.length > 0 ? (
                <div className="space-y-2 mt-2">
                  {editingTTC.upcomingDates.map((date, index) => (
                    <div key={index} className="flex justify-between items-center bg-yellow-50 p-2 rounded-md">
                      <span>
                        {new Date(date.start).toLocaleDateString()} to {new Date(date.end).toLocaleDateString()} - {date.status}
                      </span>
                      <button
                        type="button"
                        onClick={() => removeDateOption(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        &times;
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 italic">No dates added yet.</p>
              )}
            </div>

            {/* Application Process */}
            <div className="grid gap-2">
              <label htmlFor="applicationProcess" className="font-medium text-sm">
                Application Process
              </label>
              <textarea
                id="applicationProcess"
                name="applicationProcess"
                value={editingTTC.applicationProcess}
                onChange={handleInputChange}
                rows={3}
                className="w-full p-2 border rounded-md"
                placeholder="e.g., Fill out the online application form. A non-refundable deposit is required..."
              />
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-2 pt-4">
              <button
                type="button"
                onClick={cancelEditing}
                className="px-4 py-2 border border-gray-300 rounded-md"
              >
                Cancel
              </button>
              {!isNewTTC && (
                <button
                  type="button"
                  onClick={() => removeTTC(editingTTC.id)}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md"
                >
                  Delete
                </button>
              )}
              <button
                type="button"
                onClick={saveTTC}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
              >
                Save Course
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Submit Button - only show when not editing a TTC */}
      {!editingTTC && ttcs.length > 0 && (
        <form onSubmit={handleSubmit} className="pt-6">
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
          >
            Save All Courses
          </button>
        </form>
      )}
    </div>
  );
} 