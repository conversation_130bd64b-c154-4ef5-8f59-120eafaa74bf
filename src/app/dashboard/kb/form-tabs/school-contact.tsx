'use client';

import { useState, useEffect } from 'react';

interface SchoolContactFormProps {
  chatbotId: string;
  onSaveStart: () => void;
  onSaveComplete: (success: boolean) => void;
}

interface SocialMediaLink {
  platform: string;
  url: string;
}

interface SchoolContactData {
  fullAddress: string;
  googleMapsLink: string;
  howToReach: string;
  primaryPhone: string;
  whatsappNumber: string;
  primaryEmail: string;
  websiteUrl: string;
  socialMediaLinks: SocialMediaLink[];
}

export default function SchoolContactForm({
  chatbotId,
  onSaveStart,
  onSaveComplete,
}: SchoolContactFormProps) {
  const [formData, setFormData] = useState<SchoolContactData>({
    fullAddress: '',
    googleMapsLink: '',
    howToReach: '',
    primaryPhone: '',
    whatsappNumber: '',
    primaryEmail: '',
    websiteUrl: '',
    socialMediaLinks: [],
  });

  const [loading, setLoading] = useState(true);
  const [newPlatform, setNewPlatform] = useState('');
  const [newUrl, setNewUrl] = useState('');

  // Fetch existing data if available
  useEffect(() => {
    const fetchContactData = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/kb/contact?chatbotId=${chatbotId}`);
        
        if (response.ok) {
          const data = await response.json();
          if (data.contact) {
            setFormData(data.contact);
          }
        }
      } catch (error) {
        console.error('Error fetching contact data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchContactData();
  }, [chatbotId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const addSocialMediaLink = () => {
    if (newPlatform && newUrl) {
      setFormData((prev) => ({
        ...prev,
        socialMediaLinks: [
          ...prev.socialMediaLinks,
          { platform: newPlatform, url: newUrl }
        ]
      }));
      setNewPlatform('');
      setNewUrl('');
    }
  };

  const removeSocialMediaLink = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      socialMediaLinks: prev.socialMediaLinks.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      onSaveStart();
      
      const response = await fetch('/api/kb/save-contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chatbotId,
          contact: formData,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to save contact data');
      }
      
      onSaveComplete(true);
    } catch (error) {
      console.error('Error saving contact data:', error);
      onSaveComplete(false);
    }
  };

  if (loading) {
    return <div className="p-4 text-center">Loading...</div>;
  }

  // Social media platforms for dropdown
  const socialPlatforms = [
    'Facebook', 'Instagram', 'YouTube', 'TikTok', 'LinkedIn', 
    'Twitter', 'Pinterest', 'WhatsApp', 'Telegram', 'Other'
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="mb-8">
        <h3 className="text-lg font-medium mb-2">Contact, Location & Social Media</h3>
        <p className="text-gray-600 text-sm mb-4">
          Provide practical information for contacting your school and details about its online presence.
        </p>
      </div>

      {/* Full Address */}
      <div className="grid gap-2">
        <label htmlFor="fullAddress" className="font-medium text-sm">
          Full Address
        </label>
        <textarea
          id="fullAddress"
          name="fullAddress"
          value={formData.fullAddress}
          onChange={handleInputChange}
          rows={3}
          className="w-full p-2 border rounded-md"
          placeholder="e.g., Mountain View Road, Upper Tapovan, Town, State, 249192, Country"
        />
        <p className="text-xs text-gray-500">The complete physical street address of your school.</p>
      </div>

      {/* Google Maps Link */}
      <div className="grid gap-2">
        <label htmlFor="googleMapsLink" className="font-medium text-sm">
          Google Maps Link
        </label>
        <input
          id="googleMapsLink"
          name="googleMapsLink"
          type="url"
          value={formData.googleMapsLink}
          onChange={handleInputChange}
          className="w-full p-2 border rounded-md"
          placeholder="e.g., https://maps.app.goo.gl/..."
        />
        <p className="text-xs text-gray-500">The direct share link to your location on Google Maps.</p>
      </div>

      {/* How To Reach */}
      <div className="grid gap-2">
        <label htmlFor="howToReach" className="font-medium text-sm">
          How To Reach
        </label>
        <textarea
          id="howToReach"
          name="howToReach"
          value={formData.howToReach}
          onChange={handleInputChange}
          rows={4}
          className="w-full p-2 border rounded-md"
          placeholder="e.g., The nearest airport is... From there, a pre-paid taxi takes..."
        />
        <p className="text-xs text-gray-500">Detailed instructions from the nearest airports, train, and bus stations.</p>
      </div>

      {/* Primary Phone */}
      <div className="grid gap-2">
        <label htmlFor="primaryPhone" className="font-medium text-sm">
          Primary Phone
        </label>
        <input
          id="primaryPhone"
          name="primaryPhone"
          type="tel"
          value={formData.primaryPhone}
          onChange={handleInputChange}
          className="w-full p-2 border rounded-md"
          placeholder="e.g., +91 ************"
        />
        <p className="text-xs text-gray-500">Main contact phone number, including country code.</p>
      </div>

      {/* WhatsApp Number */}
      <div className="grid gap-2">
        <label htmlFor="whatsappNumber" className="font-medium text-sm">
          WhatsApp Number
        </label>
        <input
          id="whatsappNumber"
          name="whatsappNumber"
          type="tel"
          value={formData.whatsappNumber}
          onChange={handleInputChange}
          className="w-full p-2 border rounded-md"
          placeholder="e.g., +91 ************"
        />
        <p className="text-xs text-gray-500">Dedicated number for WhatsApp inquiries, if different from primary phone.</p>
      </div>

      {/* Primary Email */}
      <div className="grid gap-2">
        <label htmlFor="primaryEmail" className="font-medium text-sm">
          Primary Email
        </label>
        <input
          id="primaryEmail"
          name="primaryEmail"
          type="email"
          value={formData.primaryEmail}
          onChange={handleInputChange}
          className="w-full p-2 border rounded-md"
          placeholder="e.g., <EMAIL>"
        />
        <p className="text-xs text-gray-500">Main contact email address for inquiries.</p>
      </div>

      {/* Website URL */}
      <div className="grid gap-2">
        <label htmlFor="websiteUrl" className="font-medium text-sm">
          Website URL
        </label>
        <input
          id="websiteUrl"
          name="websiteUrl"
          type="url"
          value={formData.websiteUrl}
          onChange={handleInputChange}
          className="w-full p-2 border rounded-md"
          placeholder="e.g., https://www.example.com"
        />
        <p className="text-xs text-gray-500">The school's main website.</p>
      </div>

      {/* Social Media Links */}
      <div className="grid gap-2">
        <label className="font-medium text-sm">Social Media Links</label>
        
        {/* Add new social media link */}
        <div className="flex gap-2 mb-2">
          <select
            value={newPlatform}
            onChange={(e) => setNewPlatform(e.target.value)}
            className="p-2 border rounded-md flex-grow-0"
          >
            <option value="">Select platform...</option>
            {socialPlatforms.map((platform) => (
              <option key={platform} value={platform}>
                {platform}
              </option>
            ))}
          </select>
          <input
            type="url"
            value={newUrl}
            onChange={(e) => setNewUrl(e.target.value)}
            placeholder="Enter URL..."
            className="p-2 border rounded-md flex-grow"
          />
          <button
            type="button"
            onClick={addSocialMediaLink}
            className="px-4 py-2 bg-blue-600 text-white rounded-md"
            disabled={!newPlatform || !newUrl}
          >
            Add
          </button>
        </div>

        {/* List of social media links */}
        {formData.socialMediaLinks.length > 0 ? (
          <div className="space-y-2">
            {formData.socialMediaLinks.map((link, index) => (
              <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded-md">
                <span className="font-medium">{link.platform}:</span>
                <span className="text-blue-600 truncate flex-grow">{link.url}</span>
                <button
                  type="button"
                  onClick={() => removeSocialMediaLink(index)}
                  className="text-red-500 hover:text-red-700"
                >
                  Remove
                </button>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-sm text-gray-500 italic">No social media links added yet.</p>
        )}
      </div>

      {/* Submit Button */}
      <div className="pt-4">
        <button
          type="submit"
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
        >
          Save Contact Information
        </button>
      </div>
    </form>
  );
} 