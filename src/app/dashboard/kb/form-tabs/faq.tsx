'use client';

import { useState, useEffect } from 'react';

interface FAQFormProps {
  chatbotId: string;
  onSaveStart: () => void;
  onSaveComplete: (success: boolean) => void;
}

interface FAQ {
  id?: string;
  question: string;
  answer: string;
}

export default function FAQForm({
  chatbotId,
  onSaveStart,
  onSaveComplete,
}: FAQFormProps) {
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingFaq, setEditingFaq] = useState<FAQ | null>(null);
  const [isNewFaq, setIsNewFaq] = useState(false);

  // Fetch existing data if available
  useEffect(() => {
    const fetchFaqs = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/kb/faq?chatbotId=${chatbotId}`);
        
        if (response.ok) {
          const data = await response.json();
          if (data.faqs) {
            setFaqs(data.faqs);
          }
        }
      } catch (error) {
        console.error('Error fetching FAQ data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFaqs();
  }, [chatbotId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    if (editingFaq) {
      setEditingFaq({
        ...editingFaq,
        [name]: value,
      });
    }
  };

  const startAddingFaq = () => {
    setEditingFaq({
      question: '',
      answer: '',
    });
    setIsNewFaq(true);
  };

  const startEditingFaq = (faq: FAQ) => {
    setEditingFaq({ ...faq });
    setIsNewFaq(false);
  };

  const cancelEditing = () => {
    setEditingFaq(null);
  };

  const saveFaq = () => {
    if (editingFaq && editingFaq.question.trim() && editingFaq.answer.trim()) {
      if (isNewFaq) {
        // Add new FAQ to the list
        setFaqs([...faqs, editingFaq]);
      } else {
        // Update existing FAQ
        setFaqs(faqs.map(f => 
          f.id === editingFaq.id ? editingFaq : f
        ));
      }
      setEditingFaq(null);
    }
  };

  const removeFaq = (faqId: string | undefined) => {
    if (faqId) {
      setFaqs(faqs.filter(f => f.id !== faqId));
    } else {
      // For new FAQs that don't have an ID yet
      const index = faqs.findIndex(f => f === editingFaq);
      if (index !== -1) {
        const updatedFaqs = [...faqs];
        updatedFaqs.splice(index, 1);
        setFaqs(updatedFaqs);
      }
    }
    setEditingFaq(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      onSaveStart();
      
      const response = await fetch('/api/kb/save-faq', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chatbotId,
          faqs,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to save FAQ data');
      }
      
      onSaveComplete(true);
    } catch (error) {
      console.error('Error saving FAQ data:', error);
      onSaveComplete(false);
    }
  };

  if (loading) {
    return <div className="p-4 text-center">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="mb-8">
        <h3 className="text-lg font-medium mb-2">Frequently Asked Questions</h3>
        <p className="text-gray-600 text-sm mb-4">
          Add common questions and answers about your school, programs, and services.
        </p>
      </div>

      {/* FAQ List */}
      {!editingFaq && (
        <>
          <div className="space-y-4">
            {faqs.length > 0 ? (
              faqs.map((faq, index) => (
                <div key={faq.id || index} className="p-4 border rounded-md bg-white">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">{faq.question}</h4>
                      <p className="text-gray-600 text-sm mt-1">
                        {faq.answer.length > 100 ? `${faq.answer.substring(0, 100)}...` : faq.answer}
                      </p>
                    </div>
                    <button
                      type="button"
                      onClick={() => startEditingFaq(faq)}
                      className="px-3 py-1 bg-blue-100 text-blue-700 rounded-md text-sm"
                    >
                      Edit
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-center text-gray-500 p-4 border-2 border-dashed rounded-md">
                No FAQs added yet. Click the button below to add your first FAQ.
              </p>
            )}
          </div>

          <div className="flex justify-center">
            <button
              type="button"
              onClick={startAddingFaq}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
              Add FAQ
            </button>
          </div>
        </>
      )}

      {/* FAQ Edit Form */}
      {editingFaq && (
        <div className="border rounded-md p-4 bg-gray-50">
          <h4 className="font-medium mb-4">{isNewFaq ? 'Add New FAQ' : 'Edit FAQ'}</h4>
          
          <div className="space-y-4">
            {/* Question */}
            <div className="grid gap-2">
              <label htmlFor="question" className="font-medium text-sm">
                Question <span className="text-red-500">*</span>
              </label>
              <input
                id="question"
                name="question"
                type="text"
                value={editingFaq.question}
                onChange={handleInputChange}
                required
                className="w-full p-2 border rounded-md"
                placeholder="e.g., Is the location safe for solo female travelers?"
              />
            </div>

            {/* Answer */}
            <div className="grid gap-2">
              <label htmlFor="answer" className="font-medium text-sm">
                Answer <span className="text-red-500">*</span>
              </label>
              <textarea
                id="answer"
                name="answer"
                value={editingFaq.answer}
                onChange={handleInputChange}
                rows={5}
                className="w-full p-2 border rounded-md"
                placeholder="e.g., Yes, the area is considered very safe..."
                required
              />
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-2 pt-4">
              <button
                type="button"
                onClick={cancelEditing}
                className="px-4 py-2 border border-gray-300 rounded-md"
              >
                Cancel
              </button>
              {!isNewFaq && (
                <button
                  type="button"
                  onClick={() => removeFaq(editingFaq.id)}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md"
                >
                  Delete
                </button>
              )}
              <button
                type="button"
                onClick={saveFaq}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
              >
                Save FAQ
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Submit Button - only show when not editing an FAQ */}
      {!editingFaq && faqs.length > 0 && (
        <form onSubmit={handleSubmit} className="pt-6">
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
          >
            Save All FAQs
          </button>
        </form>
      )}
    </div>
  );
}
