'use client';

import { useState, useEffect } from 'react';

interface RetreatsFormProps {
  chatbotId: string;
  onSaveStart: () => void;
  onSaveComplete: (success: boolean) => void;
}

interface PriceOption {
  type: string;
  price: number;
  currency: string;
}

interface DateOption {
  start: string;
  end: string;
  status: string;
}

interface Retreat {
  id?: string;
  name: string;
  theme: string;
  duration: string;
  yogaStyle: string;
  intendedAudience: string;
  highlights: string[];
  priceOptions: PriceOption[];
  upcomingDates: DateOption[];
}

export default function RetreatsForm({
  chatbotId,
  onSaveStart,
  onSaveComplete,
}: RetreatsFormProps) {
  const [retreats, setRetreats] = useState<Retreat[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingRetreat, setEditingRetreat] = useState<Retreat | null>(null);
  const [isNewRetreat, setIsNewRetreat] = useState(false);
  
  // For list inputs
  const [newHighlight, setNewHighlight] = useState('');
  
  // For price options
  const [newPriceOption, setNewPriceOption] = useState<PriceOption>({
    type: '',
    price: 0,
    currency: 'USD'
  });
  
  // For date options
  const [newDateOption, setNewDateOption] = useState<DateOption>({
    start: '',
    end: '',
    status: 'Open'
  });

  // Fetch existing data if available
  useEffect(() => {
    const fetchRetreats = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/kb/retreat?chatbotId=${chatbotId}`);
        
        if (response.ok) {
          const data = await response.json();
          if (data.retreats) {
            setRetreats(data.retreats);
          }
        }
      } catch (error) {
        console.error('Error fetching retreat data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRetreats();
  }, [chatbotId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    if (editingRetreat) {
      setEditingRetreat({
        ...editingRetreat,
        [name]: value,
      });
    }
  };

  // Price option handlers
  const handlePriceOptionChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewPriceOption({
      ...newPriceOption,
      [name]: name === 'price' ? parseFloat(value) : value,
    });
  };

  const addPriceOption = () => {
    if (newPriceOption.type && newPriceOption.price > 0 && editingRetreat) {
      setEditingRetreat({
        ...editingRetreat,
        priceOptions: [...editingRetreat.priceOptions, { ...newPriceOption }]
      });
      setNewPriceOption({
        type: '',
        price: 0,
        currency: 'USD'
      });
    }
  };

  const removePriceOption = (index: number) => {
    if (editingRetreat) {
      const updatedPriceOptions = [...editingRetreat.priceOptions];
      updatedPriceOptions.splice(index, 1);
      setEditingRetreat({
        ...editingRetreat,
        priceOptions: updatedPriceOptions
      });
    }
  };

  // Date option handlers
  const handleDateOptionChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewDateOption({
      ...newDateOption,
      [name]: value,
    });
  };

  const addDateOption = () => {
    if (newDateOption.start && newDateOption.end && editingRetreat) {
      setEditingRetreat({
        ...editingRetreat,
        upcomingDates: [...editingRetreat.upcomingDates, { ...newDateOption }]
      });
      setNewDateOption({
        start: '',
        end: '',
        status: 'Open'
      });
    }
  };

  const removeDateOption = (index: number) => {
    if (editingRetreat) {
      const updatedDateOptions = [...editingRetreat.upcomingDates];
      updatedDateOptions.splice(index, 1);
      setEditingRetreat({
        ...editingRetreat,
        upcomingDates: updatedDateOptions
      });
    }
  };

  // Highlight handlers
  const addHighlight = () => {
    if (newHighlight && editingRetreat) {
      setEditingRetreat({
        ...editingRetreat,
        highlights: [...editingRetreat.highlights, newHighlight]
      });
      setNewHighlight('');
    }
  };

  const removeHighlight = (index: number) => {
    if (editingRetreat) {
      const updatedHighlights = [...editingRetreat.highlights];
      updatedHighlights.splice(index, 1);
      setEditingRetreat({
        ...editingRetreat,
        highlights: updatedHighlights
      });
    }
  };

  const startAddingRetreat = () => {
    setEditingRetreat({
      name: '',
      theme: '',
      duration: '',
      yogaStyle: '',
      intendedAudience: '',
      highlights: [],
      priceOptions: [],
      upcomingDates: []
    });
    setIsNewRetreat(true);
  };

  const startEditingRetreat = (retreat: Retreat) => {
    setEditingRetreat({ ...retreat });
    setIsNewRetreat(false);
  };

  const cancelEditing = () => {
    setEditingRetreat(null);
  };

  const saveRetreat = () => {
    if (editingRetreat) {
      if (isNewRetreat) {
        // Add new retreat to the list
        setRetreats([...retreats, editingRetreat]);
      } else {
        // Update existing retreat
        setRetreats(retreats.map(r => 
          r.id === editingRetreat.id ? editingRetreat : r
        ));
      }
      setEditingRetreat(null);
    }
  };

  const removeRetreat = (retreatId: string | undefined) => {
    if (retreatId) {
      setRetreats(retreats.filter(r => r.id !== retreatId));
    } else {
      // For new retreats that don't have an ID yet
      const index = retreats.findIndex(r => r === editingRetreat);
      if (index !== -1) {
        const updatedRetreats = [...retreats];
        updatedRetreats.splice(index, 1);
        setRetreats(updatedRetreats);
      }
    }
    setEditingRetreat(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      onSaveStart();
      
      const response = await fetch('/api/kb/save-retreat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chatbotId,
          retreats,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to save retreat data');
      }
      
      onSaveComplete(true);
    } catch (error) {
      console.error('Error saving retreat data:', error);
      onSaveComplete(false);
    }
  };

  if (loading) {
    return <div className="p-4 text-center">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="mb-8">
        <h3 className="text-lg font-medium mb-2">Yoga & Wellness Retreats</h3>
        <p className="text-gray-600 text-sm mb-4">
          Add information about your yoga retreats and wellness experiences. These are typically non-certification immersive programs.
        </p>
      </div>

      {/* Retreat List */}
      {!editingRetreat && (
        <>
          <div className="space-y-4">
            {retreats.length > 0 ? (
              retreats.map((retreat, index) => (
                <div key={retreat.id || index} className="p-4 border rounded-md bg-white">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">{retreat.name}</h4>
                      <p className="text-gray-600 text-sm">{retreat.duration} • {retreat.theme}</p>
                    </div>
                    <button
                      type="button"
                      onClick={() => startEditingRetreat(retreat)}
                      className="px-3 py-1 bg-blue-100 text-blue-700 rounded-md text-sm"
                    >
                      Edit
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-center text-gray-500 p-4 border-2 border-dashed rounded-md">
                No retreats added yet. Click the button below to add your first retreat.
              </p>
            )}
          </div>

          <div className="flex justify-center">
            <button
              type="button"
              onClick={startAddingRetreat}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
              Add Retreat
            </button>
          </div>
        </>
      )}

      {/* Retreat Edit Form */}
      {editingRetreat && (
        <div className="border rounded-md p-4 bg-gray-50">
          <h4 className="font-medium mb-4">{isNewRetreat ? 'Add New Retreat' : 'Edit Retreat'}</h4>
          
          <div className="space-y-4">
            {/* Retreat Name */}
            <div className="grid gap-2">
              <label htmlFor="name" className="font-medium text-sm">
                Retreat Name <span className="text-red-500">*</span>
              </label>
              <input
                id="name"
                name="name"
                type="text"
                value={editingRetreat.name}
                onChange={handleInputChange}
                required
                className="w-full p-2 border rounded-md"
                placeholder="e.g., 7-Day Silent Meditation & Digital Detox Retreat"
              />
            </div>

            {/* Retreat Theme */}
            <div className="grid gap-2">
              <label htmlFor="theme" className="font-medium text-sm">
                Retreat Theme
              </label>
              <input
                id="theme"
                name="theme"
                type="text"
                value={editingRetreat.theme}
                onChange={handleInputChange}
                className="w-full p-2 border rounded-md"
                placeholder="e.g., Cultivating inner peace by disconnecting from digital distractions..."
              />
            </div>

            {/* Retreat Duration */}
            <div className="grid gap-2">
              <label htmlFor="duration" className="font-medium text-sm">
                Duration
              </label>
              <input
                id="duration"
                name="duration"
                type="text"
                value={editingRetreat.duration}
                onChange={handleInputChange}
                className="w-full p-2 border rounded-md"
                placeholder="e.g., 7 Days, 6 Nights"
              />
            </div>

            {/* Yoga Style */}
            <div className="grid gap-2">
              <label htmlFor="yogaStyle" className="font-medium text-sm">
                Yoga Style
              </label>
              <input
                id="yogaStyle"
                name="yogaStyle"
                type="text"
                value={editingRetreat.yogaStyle}
                onChange={handleInputChange}
                className="w-full p-2 border rounded-md"
                placeholder="e.g., Gentle Hatha, Yin Yoga, Yoga Nidra"
              />
            </div>

            {/* Intended Audience */}
            <div className="grid gap-2">
              <label htmlFor="intendedAudience" className="font-medium text-sm">
                Intended Audience
              </label>
              <input
                id="intendedAudience"
                name="intendedAudience"
                type="text"
                value={editingRetreat.intendedAudience}
                onChange={handleInputChange}
                className="w-full p-2 border rounded-md"
                placeholder="e.g., All Levels, especially those feeling stressed or burnt out"
              />
            </div>

            {/* Highlights */}
            <div className="grid gap-2">
              <label className="font-medium text-sm">
                Highlights
              </label>
              
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newHighlight}
                  onChange={(e) => setNewHighlight(e.target.value)}
                  className="w-full p-2 border rounded-md"
                  placeholder="e.g., Guided silent meditation, nature walks..."
                />
                <button
                  type="button"
                  onClick={addHighlight}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md"
                  disabled={!newHighlight}
                >
                  Add
                </button>
              </div>

              {/* List of highlights */}
              {editingRetreat.highlights.length > 0 ? (
                <div className="space-y-2 mt-2">
                  {editingRetreat.highlights.map((highlight, index) => (
                    <div key={index} className="flex justify-between items-center bg-blue-50 p-2 rounded-md">
                      <span>{highlight}</span>
                      <button
                        type="button"
                        onClick={() => removeHighlight(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        &times;
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 italic">No highlights added yet.</p>
              )}
            </div>

            {/* Price Options */}
            <div className="grid gap-2">
              <label className="font-medium text-sm">
                Price Options
              </label>
              
              <div className="flex flex-wrap gap-2">
                <input
                  type="text"
                  name="type"
                  value={newPriceOption.type}
                  onChange={handlePriceOptionChange}
                  className="flex-1 p-2 border rounded-md"
                  placeholder="Room Type (e.g., Shared Twin)"
                />
                <input
                  type="number"
                  name="price"
                  value={newPriceOption.price || ''}
                  onChange={handlePriceOptionChange}
                  className="w-24 p-2 border rounded-md"
                  placeholder="Price"
                  min="0"
                />
                <select
                  name="currency"
                  value={newPriceOption.currency}
                  onChange={handlePriceOptionChange}
                  className="w-24 p-2 border rounded-md"
                >
                  <option value="USD">USD</option>
                  <option value="EUR">EUR</option>
                  <option value="GBP">GBP</option>
                  <option value="INR">INR</option>
                </select>
                <button
                  type="button"
                  onClick={addPriceOption}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md"
                  disabled={!newPriceOption.type || newPriceOption.price <= 0}
                >
                  Add
                </button>
              </div>

              {/* List of price options */}
              {editingRetreat.priceOptions.length > 0 ? (
                <div className="space-y-2 mt-2">
                  {editingRetreat.priceOptions.map((option, index) => (
                    <div key={index} className="flex justify-between items-center bg-green-50 p-2 rounded-md">
                      <span>
                        {option.type}: {option.price} {option.currency}
                      </span>
                      <button
                        type="button"
                        onClick={() => removePriceOption(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        &times;
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 italic">No price options added yet.</p>
              )}
            </div>

            {/* Upcoming Dates */}
            <div className="grid gap-2">
              <label className="font-medium text-sm">
                Upcoming Dates
              </label>
              
              <div className="flex flex-wrap gap-2">
                <input
                  type="date"
                  name="start"
                  value={newDateOption.start}
                  onChange={handleDateOptionChange}
                  className="flex-1 p-2 border rounded-md"
                  placeholder="Start Date"
                />
                <input
                  type="date"
                  name="end"
                  value={newDateOption.end}
                  onChange={handleDateOptionChange}
                  className="flex-1 p-2 border rounded-md"
                  placeholder="End Date"
                />
                <select
                  name="status"
                  value={newDateOption.status}
                  onChange={handleDateOptionChange}
                  className="w-32 p-2 border rounded-md"
                >
                  <option value="Open">Open</option>
                  <option value="Filling Fast">Filling Fast</option>
                  <option value="Almost Full">Almost Full</option>
                  <option value="Full">Full</option>
                  <option value="Waitlist">Waitlist</option>
                </select>
                <button
                  type="button"
                  onClick={addDateOption}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md"
                  disabled={!newDateOption.start || !newDateOption.end}
                >
                  Add
                </button>
              </div>

              {/* List of date options */}
              {editingRetreat.upcomingDates.length > 0 ? (
                <div className="space-y-2 mt-2">
                  {editingRetreat.upcomingDates.map((date, index) => (
                    <div key={index} className="flex justify-between items-center bg-yellow-50 p-2 rounded-md">
                      <span>
                        {new Date(date.start).toLocaleDateString()} to {new Date(date.end).toLocaleDateString()} - {date.status}
                      </span>
                      <button
                        type="button"
                        onClick={() => removeDateOption(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        &times;
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500 italic">No dates added yet.</p>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-2 pt-4">
              <button
                type="button"
                onClick={cancelEditing}
                className="px-4 py-2 border border-gray-300 rounded-md"
              >
                Cancel
              </button>
              {!isNewRetreat && (
                <button
                  type="button"
                  onClick={() => removeRetreat(editingRetreat.id)}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md"
                >
                  Delete
                </button>
              )}
              <button
                type="button"
                onClick={saveRetreat}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
              >
                Save Retreat
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Submit Button - only show when not editing a retreat */}
      {!editingRetreat && retreats.length > 0 && (
        <form onSubmit={handleSubmit} className="pt-6">
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
          >
            Save All Retreats
          </button>
        </form>
      )}
    </div>
  );
} 