'use client';

import { useState, useEffect } from 'react';

interface FacilitiesFormProps {
  chatbotId: string;
  onSaveStart: () => void;
  onSaveComplete: (success: boolean) => void;
}

interface Photo {
  url: string;
  caption: string;
}

interface Facility {
  accommodationDetails: string;
  foodPhilosophy: string;
  otherAmenities: string;
  photoGallery: Photo[];
  videoTourLink: string;
}

export default function FacilitiesForm({
  chatbotId,
  onSaveStart,
  onSaveComplete,
}: FacilitiesFormProps) {
  const [facility, setFacility] = useState<Facility>({
    accommodationDetails: '',
    foodPhilosophy: '',
    otherAmenities: '',
    photoGallery: [],
    videoTourLink: '',
  });
  
  const [loading, setLoading] = useState(true);
  const [newPhotoUrl, setNewPhotoUrl] = useState('');
  const [newPhotoCaption, setNewPhotoCaption] = useState('');

  // Fetch existing data if available
  useEffect(() => {
    const fetchFacility = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/kb/facility?chatbotId=${chatbotId}`);
        
        if (response.ok) {
          const data = await response.json();
          if (data.facility) {
            // Parse the photoGallery JSON string if it exists
            const photoGallery = data.facility.photoGallery 
              ? typeof data.facility.photoGallery === 'string' 
                ? JSON.parse(data.facility.photoGallery) 
                : data.facility.photoGallery
              : [];
              
            setFacility({
              accommodationDetails: data.facility.accommodationDetails || '',
              foodPhilosophy: data.facility.foodPhilosophy || '',
              otherAmenities: data.facility.otherAmenities || '',
              photoGallery: photoGallery || [],
              videoTourLink: data.facility.videoTourLink || '',
            });
          }
        }
      } catch (error) {
        console.error('Error fetching facility data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFacility();
  }, [chatbotId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFacility({
      ...facility,
      [name]: value,
    });
  };

  const handleAddPhoto = () => {
    if (newPhotoUrl.trim()) {
      const newPhoto: Photo = {
        url: newPhotoUrl,
        caption: newPhotoCaption,
      };
      
      setFacility({
        ...facility,
        photoGallery: [...facility.photoGallery, newPhoto],
      });
      
      // Reset inputs
      setNewPhotoUrl('');
      setNewPhotoCaption('');
    }
  };

  const handleRemovePhoto = (index: number) => {
    const updatedGallery = [...facility.photoGallery];
    updatedGallery.splice(index, 1);
    
    setFacility({
      ...facility,
      photoGallery: updatedGallery,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      onSaveStart();
      
      const response = await fetch('/api/kb/save-facility', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chatbotId,
          facility,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to save facility data');
      }
      
      onSaveComplete(true);
    } catch (error) {
      console.error('Error saving facility data:', error);
      onSaveComplete(false);
    }
  };

  if (loading) {
    return <div className="p-4 text-center">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="mb-8">
        <h3 className="text-lg font-medium mb-2">Facilities, Accommodation & Food</h3>
        <p className="text-gray-600 text-sm mb-4">
          Add information about your physical environment and living experience.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Accommodation Details */}
        <div className="grid gap-2">
          <label htmlFor="accommodationDetails" className="font-medium text-sm">
            Accommodation Details
          </label>
          <textarea
            id="accommodationDetails"
            name="accommodationDetails"
            value={facility.accommodationDetails}
            onChange={handleInputChange}
            rows={4}
            className="w-full p-2 border rounded-md"
            placeholder="e.g., We offer clean and simple rooms designed for restful sleep..."
          />
        </div>

        {/* Food Philosophy */}
        <div className="grid gap-2">
          <label htmlFor="foodPhilosophy" className="font-medium text-sm">
            Food Philosophy
          </label>
          <textarea
            id="foodPhilosophy"
            name="foodPhilosophy"
            value={facility.foodPhilosophy}
            onChange={handleInputChange}
            rows={4}
            className="w-full p-2 border rounded-md"
            placeholder="e.g., Our kitchen serves three delicious, sattvic, vegetarian meals per day..."
          />
        </div>

        {/* Other Amenities */}
        <div className="grid gap-2">
          <label htmlFor="otherAmenities" className="font-medium text-sm">
            Other Amenities
          </label>
          <textarea
            id="otherAmenities"
            name="otherAmenities"
            value={facility.otherAmenities}
            onChange={handleInputChange}
            rows={4}
            className="w-full p-2 border rounded-md"
            placeholder="e.g., Two spacious yoga shalas, a spiritual library, a garden..."
          />
        </div>

        {/* Photo Gallery */}
        <div className="space-y-4">
          <label className="font-medium text-sm block">Photo Gallery</label>
          
          {/* Existing Photos */}
          {facility.photoGallery.length > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              {facility.photoGallery.map((photo, index) => (
                <div key={index} className="border rounded-md p-3 bg-white">
                  <div className="flex justify-between items-start mb-2">
                    <p className="text-sm truncate flex-1">{photo.url}</p>
                    <button
                      type="button"
                      onClick={() => handleRemovePhoto(index)}
                      className="text-red-500 hover:text-red-700 ml-2"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>
                  {photo.caption && (
                    <p className="text-xs text-gray-500 italic">{photo.caption}</p>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Add New Photo */}
          <div className="border rounded-md p-4 bg-gray-50">
            <h4 className="font-medium text-sm mb-3">Add New Photo</h4>
            <div className="space-y-3">
              <div>
                <label htmlFor="newPhotoUrl" className="text-xs font-medium block mb-1">
                  Photo URL <span className="text-red-500">*</span>
                </label>
                <input
                  id="newPhotoUrl"
                  type="url"
                  value={newPhotoUrl}
                  onChange={(e) => setNewPhotoUrl(e.target.value)}
                  className="w-full p-2 border rounded-md"
                  placeholder="e.g., https://example.com/photos/shala1.jpg"
                />
              </div>
              <div>
                <label htmlFor="newPhotoCaption" className="text-xs font-medium block mb-1">
                  Caption
                </label>
                <input
                  id="newPhotoCaption"
                  type="text"
                  value={newPhotoCaption}
                  onChange={(e) => setNewPhotoCaption(e.target.value)}
                  className="w-full p-2 border rounded-md"
                  placeholder="e.g., Our main yoga hall"
                />
              </div>
              <button
                type="button"
                onClick={handleAddPhoto}
                className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded-md text-sm"
              >
                Add Photo
              </button>
            </div>
          </div>
        </div>

        {/* Video Tour Link */}
        <div className="grid gap-2">
          <label htmlFor="videoTourLink" className="font-medium text-sm">
            Video Tour Link
          </label>
          <input
            id="videoTourLink"
            name="videoTourLink"
            type="url"
            value={facility.videoTourLink}
            onChange={handleInputChange}
            className="w-full p-2 border rounded-md"
            placeholder="e.g., https://youtube.com/watch?v=..."
          />
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
          >
            Save Facilities Information
          </button>
        </div>
      </form>
    </div>
  );
}
