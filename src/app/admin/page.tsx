'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { redirect } from 'next/navigation';

interface DashboardStats {
  totalUsers: number;
  totalPlans: number;
  activePlans: number;
  activeSubscriptions: number;
  totalChatbots: number;
}

export default function AdminDashboard() {
  const { data: session, status } = useSession();
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalPlans: 0,
    activePlans: 0,
    activeSubscriptions: 0,
    totalChatbots: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Redirect if not authenticated or not an admin
  useEffect(() => {
    if (status === 'unauthenticated') {
      redirect('/login');
    } else if (status === 'authenticated' && session?.user?.role !== 'ADMIN') {
      redirect('/dashboard');
    }
  }, [status, session]);

  // Fetch dashboard stats
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        
        // Fetch users
        const usersResponse = await fetch('/api/admin/users');
        if (!usersResponse.ok) throw new Error('Failed to fetch users');
        const users = await usersResponse.json();
        
        // Fetch plans
        const plansResponse = await fetch('/api/admin/plans');
        if (!plansResponse.ok) throw new Error('Failed to fetch plans');
        const plans = await plansResponse.json();
        
        // Calculate stats
        const activeSubscriptions = users.filter(
          (user: any) => user.subscription?.status === 'active'
        ).length;
        
        const activePlans = plans.filter((plan: any) => plan.isActive).length;
        
        const totalChatbots = users.filter((user: any) => user.chatbot).length;
        
        setStats({
          totalUsers: users.length,
          totalPlans: plans.length,
          activePlans,
          activeSubscriptions,
          totalChatbots,
        });
        
        setError(null);
      } catch (err) {
        console.error('Error fetching stats:', err);
        setError('Failed to load dashboard statistics');
      } finally {
        setLoading(false);
      }
    };
    
    if (status === 'authenticated' && session?.user?.role === 'ADMIN') {
      fetchStats();
    }
  }, [status, session]);

  if (status === 'loading' || loading) {
    return <div className="p-8 text-center">Loading...</div>;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-8">Admin Dashboard</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-2">Users</h2>
          <p className="text-3xl font-bold">{stats.totalUsers}</p>
          <p className="text-gray-500 mt-2">{stats.activeSubscriptions} active subscriptions</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-2">Plans</h2>
          <p className="text-3xl font-bold">{stats.totalPlans}</p>
          <p className="text-gray-500 mt-2">{stats.activePlans} active plans</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-2">Chatbots</h2>
          <p className="text-3xl font-bold">{stats.totalChatbots}</p>
        </div>
      </div>
      
      {/* Quick Actions */}
      <h2 className="text-2xl font-semibold mb-4">Quick Actions</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Link href="/admin/users" className="bg-blue-100 hover:bg-blue-200 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Manage Users</h3>
          <p className="text-gray-600">Create, view, and manage user accounts and subscriptions</p>
        </Link>
        
        <Link href="/admin/plans" className="bg-green-100 hover:bg-green-200 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Manage Plans</h3>
          <p className="text-gray-600">Create and configure subscription plans and features</p>
        </Link>
        
        <Link href="/admin/chatbots" className="bg-purple-100 hover:bg-purple-200 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Manage Chatbots</h3>
          <p className="text-gray-600">Configure and override chatbot settings</p>
        </Link>
      </div>
    </div>
  );
} 