'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface Plan {
  id: string;
  name: string;
  price: number;
  features: {
    kbType: 'simple' | 'structured';
    tokenLimit: number;
    canUseBYOK: boolean;
  };
  isActive: boolean;
}

export default function PlansPage() {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Form state for creating/editing plans
  const [isEditing, setIsEditing] = useState(false);
  const [editingPlan, setEditingPlan] = useState<Plan | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    price: 0,
    kbType: 'simple',
    tokenLimit: 50000,
    canUseBYOK: false,
    isActive: true,
  });
  
  // Fetch plans on component mount
  useEffect(() => {
    fetchPlans();
  }, []);
  
  // Fetch plans from the API
  const fetchPlans = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/plans');
      if (!response.ok) {
        throw new Error('Failed to fetch plans');
      }
      const data = await response.json();
      setPlans(data);
      setError(null);
    } catch (err) {
      setError('Error loading plans. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (type === 'number') {
      setFormData(prev => ({ ...prev, [name]: parseInt(value, 10) }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };
  
  // Start editing a plan
  const handleEdit = (plan: Plan) => {
    setIsEditing(true);
    setEditingPlan(plan);
    setFormData({
      name: plan.name,
      price: plan.price,
      kbType: plan.features.kbType,
      tokenLimit: plan.features.tokenLimit,
      canUseBYOK: plan.features.canUseBYOK,
      isActive: plan.isActive,
    });
  };
  
  // Start creating a new plan
  const handleCreate = () => {
    setIsEditing(false);
    setEditingPlan(null);
    setFormData({
      name: '',
      price: 0,
      kbType: 'simple',
      tokenLimit: 50000,
      canUseBYOK: false,
      isActive: true,
    });
  };
  
  // Cancel editing/creating
  const handleCancel = () => {
    setIsEditing(false);
    setEditingPlan(null);
  };
  
  // Submit the form to create/update a plan
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const planData = {
      name: formData.name,
      price: formData.price,
      features: {
        kbType: formData.kbType,
        tokenLimit: formData.tokenLimit,
        canUseBYOK: formData.canUseBYOK,
      },
      isActive: formData.isActive,
    };
    
    try {
      if (isEditing && editingPlan) {
        // Update existing plan
        const response = await fetch(`/api/admin/plans/${editingPlan.id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(planData),
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to update plan');
        }
      } else {
        // Create new plan
        const response = await fetch('/api/admin/plans', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(planData),
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create plan');
        }
      }
      
      // Reset form and fetch updated plans
      setIsEditing(false);
      setEditingPlan(null);
      fetchPlans();
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An unexpected error occurred');
      }
      console.error(err);
    }
  };
  
  // Delete a plan
  const handleDelete = async (planId: string) => {
    if (!confirm('Are you sure you want to delete this plan? This action cannot be undone.')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/admin/plans/${planId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete plan');
      }
      
      fetchPlans();
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An unexpected error occurred');
      }
      console.error(err);
    }
  };
  
  // Format price as currency
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price / 100);
  };
  
  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Plan Management</h1>
        <button
          onClick={handleCreate}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
        >
          Create New Plan
        </button>
      </div>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {/* Form for creating/editing plans */}
      {(isEditing || editingPlan) && (
        <div className="bg-gray-100 p-4 rounded mb-6">
          <h2 className="text-xl font-semibold mb-4">
            {isEditing ? 'Edit Plan' : 'Create New Plan'}
          </h2>
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block mb-1">Plan Name</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full border rounded px-3 py-2"
                  required
                />
              </div>
              
              <div>
                <label className="block mb-1">Price (in cents)</label>
                <input
                  type="number"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  className="w-full border rounded px-3 py-2"
                  min="0"
                  required
                />
              </div>
              
              <div>
                <label className="block mb-1">Knowledge Base Type</label>
                <select
                  name="kbType"
                  value={formData.kbType}
                  onChange={handleInputChange}
                  className="w-full border rounded px-3 py-2"
                >
                  <option value="simple">Simple Text</option>
                  <option value="structured">Structured Form</option>
                </select>
              </div>
              
              <div>
                <label className="block mb-1">Token Limit</label>
                <input
                  type="number"
                  name="tokenLimit"
                  value={formData.tokenLimit}
                  onChange={handleInputChange}
                  className="w-full border rounded px-3 py-2"
                  min="1000"
                  required
                />
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="canUseBYOK"
                  checked={formData.canUseBYOK}
                  onChange={handleInputChange}
                  className="mr-2"
                />
                <label>Can Use Own API Key (BYOK)</label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="isActive"
                  checked={formData.isActive}
                  onChange={handleInputChange}
                  className="mr-2"
                />
                <label>Is Active</label>
              </div>
            </div>
            
            <div className="mt-4 flex space-x-2">
              <button
                type="submit"
                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded"
              >
                {isEditing ? 'Update Plan' : 'Create Plan'}
              </button>
              <button
                type="button"
                onClick={handleCancel}
                className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}
      
      {/* Plans table */}
      {loading ? (
        <p>Loading plans...</p>
      ) : plans.length === 0 ? (
        <p>No plans found. Create your first plan to get started.</p>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border">
            <thead>
              <tr>
                <th className="border px-4 py-2">Name</th>
                <th className="border px-4 py-2">Price</th>
                <th className="border px-4 py-2">KB Type</th>
                <th className="border px-4 py-2">Token Limit</th>
                <th className="border px-4 py-2">BYOK</th>
                <th className="border px-4 py-2">Status</th>
                <th className="border px-4 py-2">Actions</th>
              </tr>
            </thead>
            <tbody>
              {plans.map((plan) => (
                <tr key={plan.id}>
                  <td className="border px-4 py-2">{plan.name}</td>
                  <td className="border px-4 py-2">{formatPrice(plan.price)}</td>
                  <td className="border px-4 py-2">{plan.features.kbType}</td>
                  <td className="border px-4 py-2">{plan.features.tokenLimit.toLocaleString()}</td>
                  <td className="border px-4 py-2">
                    {plan.features.canUseBYOK ? 'Yes' : 'No'}
                  </td>
                  <td className="border px-4 py-2">
                    <span
                      className={`px-2 py-1 rounded text-xs ${
                        plan.isActive
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {plan.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="border px-4 py-2">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEdit(plan)}
                        className="bg-yellow-500 hover:bg-yellow-600 text-white px-2 py-1 rounded text-sm"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDelete(plan.id)}
                        className="bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded text-sm"
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      
      <div className="mt-6">
        <Link href="/admin" className="text-blue-500 hover:underline">
          Back to Admin Dashboard
        </Link>
      </div>
    </div>
  );
} 