'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface Plan {
  id: string;
  name: string;
  price: number;
  features: {
    kbType: 'simple' | 'structured';
    tokenLimit: number;
    canUseBYOK: boolean;
  };
}

interface Subscription {
  id: string;
  status: string;
  currentPeriodEnd: string;
  tokensUsedThisPeriod: number;
  sessionsThisPeriod: number;
  plan: Plan;
}

interface Chatbot {
  id: string;
  approvedDomain: string;
}

interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  createdAt: string;
  subscription?: Subscription;
  chatbot?: Chatbot;
}

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [plans, setPlans] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Form state for creating users
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    name: '',
    password: '',
    role: 'USER',
    planId: '',
    approvedDomain: '',
  });
  
  // Fetch users and plans on component mount
  useEffect(() => {
    Promise.all([fetchUsers(), fetchPlans()]).then(() => {
      setLoading(false);
    });
  }, []);
  
  // Fetch users from the API
  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/admin/users');
      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }
      const data = await response.json();
      setUsers(data);
      setError(null);
    } catch (err) {
      setError('Error loading users. Please try again.');
      console.error(err);
    }
  };
  
  // Fetch plans from the API
  const fetchPlans = async () => {
    try {
      const response = await fetch('/api/admin/plans');
      if (!response.ok) {
        throw new Error('Failed to fetch plans');
      }
      const data = await response.json();
      setPlans(data);
      
      // Set the first plan as default if available
      if (data.length > 0) {
        setFormData(prev => ({ ...prev, planId: data[0].id }));
      }
    } catch (err) {
      setError('Error loading plans. Please try again.');
      console.error(err);
    }
  };
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  // Toggle form visibility
  const toggleForm = () => {
    setShowForm(prev => !prev);
    if (!showForm) {
      // Reset form when opening
      setFormData({
        email: '',
        name: '',
        password: '',
        role: 'USER',
        planId: plans.length > 0 ? plans[0].id : '',
        approvedDomain: '',
      });
    }
  };
  
  // Submit the form to create a new user
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const response = await fetch('/api/admin/users/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create user');
      }
      
      // Reset form and fetch updated users
      setShowForm(false);
      fetchUsers();
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An unexpected error occurred');
      }
      console.error(err);
    }
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };
  
  // Get subscription status badge class
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-yellow-100 text-yellow-800';
      case 'past_due':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">User Management</h1>
        <button
          onClick={toggleForm}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
        >
          {showForm ? 'Cancel' : 'Create New User'}
        </button>
      </div>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {/* Form for creating users */}
      {showForm && (
        <div className="bg-gray-100 p-4 rounded mb-6">
          <h2 className="text-xl font-semibold mb-4">Create New User</h2>
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block mb-1">Email</label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full border rounded px-3 py-2"
                  required
                />
              </div>
              
              <div>
                <label className="block mb-1">Name</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full border rounded px-3 py-2"
                  required
                />
              </div>
              
              <div>
                <label className="block mb-1">Password</label>
                <input
                  type="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className="w-full border rounded px-3 py-2"
                  minLength={8}
                  required
                />
              </div>
              
              <div>
                <label className="block mb-1">Role</label>
                <select
                  name="role"
                  value={formData.role}
                  onChange={handleInputChange}
                  className="w-full border rounded px-3 py-2"
                >
                  <option value="USER">User</option>
                  <option value="ADMIN">Admin</option>
                </select>
              </div>
              
              <div>
                <label className="block mb-1">Subscription Plan</label>
                <select
                  name="planId"
                  value={formData.planId}
                  onChange={handleInputChange}
                  className="w-full border rounded px-3 py-2"
                  required
                >
                  {plans.length === 0 ? (
                    <option value="">No plans available</option>
                  ) : (
                    plans.map(plan => (
                      <option key={plan.id} value={plan.id}>
                        {plan.name}
                      </option>
                    ))
                  )}
                </select>
              </div>
              
              <div>
                <label className="block mb-1">Approved Domain</label>
                <input
                  type="text"
                  name="approvedDomain"
                  value={formData.approvedDomain}
                  onChange={handleInputChange}
                  className="w-full border rounded px-3 py-2"
                  placeholder="example.com"
                  required
                />
              </div>
            </div>
            
            <div className="mt-4">
              <button
                type="submit"
                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded"
                disabled={plans.length === 0}
              >
                Create User
              </button>
            </div>
          </form>
        </div>
      )}
      
      {/* Users table */}
      {loading ? (
        <p>Loading users...</p>
      ) : users.length === 0 ? (
        <p>No users found. Create your first user to get started.</p>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border">
            <thead>
              <tr>
                <th className="border px-4 py-2">Name</th>
                <th className="border px-4 py-2">Email</th>
                <th className="border px-4 py-2">Role</th>
                <th className="border px-4 py-2">Plan</th>
                <th className="border px-4 py-2">Status</th>
                <th className="border px-4 py-2">Expires</th>
                <th className="border px-4 py-2">Domain</th>
                <th className="border px-4 py-2">Actions</th>
              </tr>
            </thead>
            <tbody>
              {users.map((user) => (
                <tr key={user.id}>
                  <td className="border px-4 py-2">{user.name}</td>
                  <td className="border px-4 py-2">{user.email}</td>
                  <td className="border px-4 py-2">
                    <span
                      className={`px-2 py-1 rounded text-xs ${
                        user.role === 'ADMIN'
                          ? 'bg-purple-100 text-purple-800'
                          : 'bg-blue-100 text-blue-800'
                      }`}
                    >
                      {user.role}
                    </span>
                  </td>
                  <td className="border px-4 py-2">
                    {user.subscription?.plan.name || 'No Plan'}
                  </td>
                  <td className="border px-4 py-2">
                    {user.subscription ? (
                      <span
                        className={`px-2 py-1 rounded text-xs ${getStatusBadgeClass(
                          user.subscription.status
                        )}`}
                      >
                        {user.subscription.status}
                      </span>
                    ) : (
                      'N/A'
                    )}
                  </td>
                  <td className="border px-4 py-2">
                    {user.subscription
                      ? formatDate(user.subscription.currentPeriodEnd)
                      : 'N/A'}
                  </td>
                  <td className="border px-4 py-2">
                    {user.chatbot?.approvedDomain || 'N/A'}
                  </td>
                  <td className="border px-4 py-2">
                    <div className="flex space-x-2">
                      {user.chatbot && (
                        <Link
                          href={`/admin/chatbots/${user.chatbot.id}`}
                          className="bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded text-sm"
                        >
                          Manage Bot
                        </Link>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      
      <div className="mt-6">
        <Link href="/admin" className="text-blue-500 hover:underline">
          Back to Admin Dashboard
        </Link>
      </div>
    </div>
  );
} 