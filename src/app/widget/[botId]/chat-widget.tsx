'use client';

import { useState, useEffect, useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';

// Define types for the widget
interface ChatWidgetProps {
  botId: string;
  config: {
    primaryColor: string;
    secondaryColor: string;
    welcomeMessage: string;
    botName: string;
    botAvatar: string;
  };
}

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'bot';
  timestamp: Date;
}

export default function ChatWidget({ botId, config }: ChatWidgetProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [visitorId, setVisitorId] = useState<string | null>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Initialize chat with welcome message and visitor ID
  useEffect(() => {
    // Generate or retrieve visitor ID from local storage
    const storedVisitorId = localStorage.getItem('yogabot_visitor_id');
    const newVisitorId = storedVisitorId || uuidv4();
    
    if (!storedVisitorId) {
      localStorage.setItem('yogabot_visitor_id', newVisitorId);
    }
    
    setVisitorId(newVisitorId);
    
    // Add welcome message
    setMessages([
      {
        id: uuidv4(),
        content: config.welcomeMessage,
        sender: 'bot',
        timestamp: new Date(),
      },
    ]);
  }, [config.welcomeMessage]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle sending a message
  const handleSendMessage = async () => {
    if (!inputValue.trim() || !visitorId) return;
    
    const userMessage: Message = {
      id: uuidv4(),
      content: inputValue,
      sender: 'user',
      timestamp: new Date(),
    };
    
    // Add user message to the chat
    setMessages((prevMessages) => [...prevMessages, userMessage]);
    setInputValue('');
    setIsLoading(true);
    
    try {
      // Send message to API
      const response = await fetch('/api/chatbot/sendMessage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          botId,
          visitorId,
          sessionId,
          message: userMessage.content,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to send message');
      }
      
      const data = await response.json();
      
      // Save session ID if it's a new conversation
      if (data.sessionId && !sessionId) {
        setSessionId(data.sessionId);
      }
      
      // Add bot response to the chat
      setMessages((prevMessages) => [
        ...prevMessages,
        {
          id: uuidv4(),
          content: data.message,
          sender: 'bot',
          timestamp: new Date(),
        },
      ]);
    } catch (error) {
      console.error('Error sending message:', error);
      
      // Add error message
      setMessages((prevMessages) => [
        ...prevMessages,
        {
          id: uuidv4(),
          content: 'Sorry, I encountered an error. Please try again later.',
          sender: 'bot',
          timestamp: new Date(),
        },
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-full max-h-[600px] bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Header */}
      <div 
        className="py-3 px-4 flex items-center"
        style={{ backgroundColor: config.primaryColor, color: 'white' }}
      >
        <div className="w-8 h-8 rounded-full overflow-hidden mr-3">
          <img 
            src={config.botAvatar || '/bot-avatar.png'} 
            alt={config.botName} 
            className="w-full h-full object-cover"
            onError={(e) => {
              e.currentTarget.src = '/bot-avatar.png';
            }}
          />
        </div>
        <h3 className="font-medium">{config.botName || 'YogaBot'}</h3>
      </div>
      
      {/* Messages */}
      <div className="flex-1 p-4 overflow-y-auto">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`mb-4 flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`py-2 px-4 rounded-lg max-w-[80%] ${
                message.sender === 'user'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-800'
              }`}
            >
              <p className="text-sm">{message.content}</p>
              <p className="text-xs mt-1 opacity-70">
                {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </p>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex justify-start mb-4">
            <div className="bg-gray-100 text-gray-800 py-2 px-4 rounded-lg">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.4s' }}></div>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>
      
      {/* Input */}
      <div className="p-4 border-t">
        <div className="flex">
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                handleSendMessage();
              }
            }}
            placeholder="Type your message..."
            className="flex-1 border rounded-l-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isLoading}
          />
          <button
            onClick={handleSendMessage}
            disabled={isLoading || !inputValue.trim()}
            className="bg-blue-500 text-white rounded-r-lg px-4 py-2 hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            style={{ backgroundColor: config.primaryColor }}
          >
            Send
          </button>
        </div>
      </div>
      
      {/* Powered by */}
      <div className="px-4 py-2 text-center text-xs text-gray-500 bg-gray-50">
        Powered by YogaBot
      </div>
    </div>
  );
} 