import { notFound } from 'next/navigation';
import { headers } from 'next/headers';
import { prisma } from '@/lib/prisma';
import ChatWidget from './chat-widget';

// Server Component for the embeddable widget
export default async function WidgetPage({ params }: { params: { botId: string } }) {
  const { botId } = params;
  const headersList = headers();
  const referer = headersList.get('referer');
  
  // Fetch the chatbot to verify it exists and check domain
  const chatbot = await prisma.chatbot.findUnique({
    where: { id: botId },
    select: {
      id: true,
      approvedDomain: true,
      widgetConfig: true,
      user: {
        select: {
          subscription: {
            select: {
              status: true,
              currentPeriodEnd: true,
            }
          }
        }
      }
    }
  });
  
  // If chatbot doesn't exist, return 404
  if (!chatbot) {
    notFound();
  }
  
  // Domain locking check
  if (referer) {
    const refererUrl = new URL(referer);
    const approvedDomain = chatbot.approvedDomain;
    
    // Check if the referer domain matches the approved domain
    // Allow localhost for development
    const isLocalhost = refererUrl.hostname === 'localhost';
    const isDomainMatch = refererUrl.hostname === approvedDomain || 
                          refererUrl.hostname.endsWith(`.${approvedDomain}`);
    
    if (!isLocalhost && !isDomainMatch) {
      return (
        <div className="flex flex-col items-center justify-center h-full bg-red-50 p-4 text-red-700 rounded-lg">
          <h2 className="text-lg font-bold mb-2">Domain Not Authorized</h2>
          <p className="text-sm text-center">
            This chatbot is not authorized for use on this domain.
            Please contact the chatbot owner to add this domain to their approved domains.
          </p>
        </div>
      );
    }
  }
  
  // Check subscription status
  const subscription = chatbot.user?.subscription;
  const isSubscriptionActive = subscription?.status === 'active' && 
                              new Date(subscription.currentPeriodEnd) > new Date();
  
  if (!isSubscriptionActive) {
    return (
      <div className="flex flex-col items-center justify-center h-full bg-yellow-50 p-4 text-yellow-700 rounded-lg">
        <h2 className="text-lg font-bold mb-2">Subscription Inactive</h2>
        <p className="text-sm text-center">
          This chatbot's subscription is not active.
          Please contact the chatbot owner to renew their subscription.
        </p>
      </div>
    );
  }
  
  // Parse widget configuration
  const widgetConfig = chatbot.widgetConfig || {
    primaryColor: '#4F46E5',
    secondaryColor: '#E5E7EB',
    welcomeMessage: 'Hello! How can I help you today?',
    botName: 'YogaBot',
    botAvatar: '/bot-avatar.png',
  };
  
  return (
    <ChatWidget 
      botId={botId}
      config={widgetConfig}
    />
  );
} 