import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { authorizeRequest, handleUnauthorized } from "@/lib/auth";
import { validateRequest, validationSchemas } from "@/lib/validation";
import { hash } from "bcryptjs";

// POST /api/admin/users/create - Create a new user with subscription and chatbot
export async function POST(req: NextRequest) {
  // Check if user is authorized (admin only)
  const authResult = await authorizeRequest(req, { requiredRole: "ADMIN" });
  const unauthorizedResponse = handleUnauthorized(authResult);
  if (unauthorizedResponse) return unauthorizedResponse;

  // Validate request body
  const validation = await validateRequest(req, validationSchemas.user.create);
  if (!validation.success) return validation.error;

  const { email, name, password, role, planId, approvedDomain } = validation.data;

  try {
    // Check if user with the same email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "A user with this email already exists" },
        { status: 400 }
      );
    }

    // Check if plan exists
    const plan = await prisma.plan.findUnique({
      where: { id: planId },
    });

    if (!plan) {
      return NextResponse.json(
        { error: "Selected plan does not exist" },
        { status: 400 }
      );
    }

    // Hash the password
    const hashedPassword = await hash(password, 12);

    // Calculate subscription end date (30 days from now)
    const currentPeriodEnd = new Date();
    currentPeriodEnd.setDate(currentPeriodEnd.getDate() + 30);

    // Create the user, subscription, and chatbot in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the user
      const user = await tx.user.create({
        data: {
          email,
          name,
          password: hashedPassword,
          role,
        },
      });

      // Create the subscription
      const subscription = await tx.subscription.create({
        data: {
          userId: user.id,
          planId,
          status: "active",
          currentPeriodEnd,
          tokensUsedThisPeriod: 0,
          sessionsThisPeriod: 0,
        },
      });

      // Create the chatbot
      const chatbot = await tx.chatbot.create({
        data: {
          userId: user.id,
          approvedDomain,
          systemPrompt: "You are YogaBot, a helpful assistant for a yoga school. Answer questions based on the provided knowledge base.",
        },
      });

      return {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
        },
        subscription,
        chatbot,
      };
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error("Error creating user:", error);
    return NextResponse.json(
      { error: "Failed to create user" },
      { status: 500 }
    );
  }
} 