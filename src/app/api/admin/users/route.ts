import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { authorizeRequest, handleUnauthorized } from "@/lib/auth";

// GET /api/admin/users - Get all users
export async function GET(req: NextRequest) {
  // Check if user is authorized (admin only)
  const authResult = await authorizeRequest(req, { requiredRole: "ADMIN" });
  const unauthorizedResponse = handleUnauthorized(authResult);
  if (unauthorizedResponse) return unauthorizedResponse;

  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        createdAt: true,
        subscription: {
          select: {
            id: true,
            status: true,
            currentPeriodEnd: true,
            tokensUsedThisPeriod: true,
            sessionsThisPeriod: true,
            plan: {
              select: {
                id: true,
                name: true,
                price: true,
                features: true,
              },
            },
          },
        },
        chatbot: {
          select: {
            id: true,
            approvedDomain: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });

    // For SQLite development: parse features from string to JSON
    const parsedUsers = users.map(user => {
      if (user.subscription && typeof user.subscription.plan.features === 'string') {
        return {
          ...user,
          subscription: {
            ...user.subscription,
            plan: {
              ...user.subscription.plan,
              features: JSON.parse(user.subscription.plan.features)
            }
          }
        };
      }
      return user;
    });

    return NextResponse.json(parsedUsers);
  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      { error: "Failed to fetch users" },
      { status: 500 }
    );
  }
} 