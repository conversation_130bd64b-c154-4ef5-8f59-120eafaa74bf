import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { authorizeRequest, handleUnauthorized } from "@/lib/auth";
import { validateRequest, validationSchemas } from "@/lib/validation";

// GET /api/admin/plans - Get all plans
export async function GET(req: NextRequest) {
  // Check if user is authorized (admin only)
  const authResult = await authorizeRequest(req, { requiredRole: "ADMIN" });
  const unauthorizedResponse = handleUnauthorized(authResult);
  if (unauthorizedResponse) return unauthorizedResponse;

  try {
    const plans = await prisma.plan.findMany({
      orderBy: { name: "asc" },
    });
    
    // For SQLite development: parse features from string to JSON
    const parsedPlans = plans.map(plan => ({
      ...plan,
      features: typeof plan.features === 'string' 
        ? JSON.parse(plan.features) 
        : plan.features
    }));
    
    return NextResponse.json(parsedPlans);
  } catch (error) {
    console.error("Error fetching plans:", error);
    return NextResponse.json(
      { error: "Failed to fetch plans" },
      { status: 500 }
    );
  }
}

// POST /api/admin/plans - Create a new plan
export async function POST(req: NextRequest) {
  // Check if user is authorized (admin only)
  const authResult = await authorizeRequest(req, { requiredRole: "ADMIN" });
  const unauthorizedResponse = handleUnauthorized(authResult);
  if (unauthorizedResponse) return unauthorizedResponse;

  // Validate request body
  const validation = await validateRequest(req, validationSchemas.plan.create);
  if (!validation.success) return validation.error;

  const { name, price, features, isActive } = validation.data;

  try {
    // Check if plan with the same name already exists
    const existingPlan = await prisma.plan.findUnique({
      where: { name },
    });

    if (existingPlan) {
      return NextResponse.json(
        { error: "A plan with this name already exists" },
        { status: 400 }
      );
    }

    // Create the new plan
    const plan = await prisma.plan.create({
      data: {
        name,
        price,
        // For SQLite development: stringify features if it's an object
        features: typeof features === 'object' ? JSON.stringify(features) : features,
        isActive,
      },
    });

    return NextResponse.json(plan, { status: 201 });
  } catch (error) {
    console.error("Error creating plan:", error);
    return NextResponse.json(
      { error: "Failed to create plan" },
      { status: 500 }
    );
  }
} 