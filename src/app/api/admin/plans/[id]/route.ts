import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { authorizeRequest, handleUnauthorized } from "@/lib/auth";
import { validateParams, validateRequest, validationSchemas } from "@/lib/validation";

// GET /api/admin/plans/[id] - Get a specific plan
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  // Check if user is authorized (admin only)
  const authResult = await authorizeRequest(req, { requiredRole: "ADMIN" });
  const unauthorizedResponse = handleUnauthorized(authResult);
  if (unauthorizedResponse) return unauthorizedResponse;

  // Validate params
  const validation = validateParams(params, validationSchemas.plan.id);
  if (!validation.success) {
    return NextResponse.json({ error: validation.error }, { status: 400 });
  }

  try {
    const plan = await prisma.plan.findUnique({
      where: { id: params.id },
    });

    if (!plan) {
      return NextResponse.json({ error: "Plan not found" }, { status: 404 });
    }

    // For SQLite development: parse features from string to JSON
    const parsedPlan = {
      ...plan,
      features: typeof plan.features === 'string' 
        ? JSON.parse(plan.features) 
        : plan.features
    };

    return NextResponse.json(parsedPlan);
  } catch (error) {
    console.error("Error fetching plan:", error);
    return NextResponse.json(
      { error: "Failed to fetch plan" },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/plans/[id] - Update a plan
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  // Check if user is authorized (admin only)
  const authResult = await authorizeRequest(req, { requiredRole: "ADMIN" });
  const unauthorizedResponse = handleUnauthorized(authResult);
  if (unauthorizedResponse) return unauthorizedResponse;

  // Validate params
  const paramsValidation = validateParams(params, validationSchemas.plan.id);
  if (!paramsValidation.success) {
    return NextResponse.json({ error: paramsValidation.error }, { status: 400 });
  }

  // Validate request body
  const bodyValidation = await validateRequest(req, validationSchemas.plan.update);
  if (!bodyValidation.success) return bodyValidation.error;

  const { name, price, features, isActive } = bodyValidation.data;

  try {
    // Check if plan exists
    const existingPlan = await prisma.plan.findUnique({
      where: { id: params.id },
    });

    if (!existingPlan) {
      return NextResponse.json({ error: "Plan not found" }, { status: 404 });
    }

    // Check if name is being changed and if it conflicts with another plan
    if (name && name !== existingPlan.name) {
      const nameConflict = await prisma.plan.findUnique({
        where: { name },
      });

      if (nameConflict) {
        return NextResponse.json(
          { error: "A plan with this name already exists" },
          { status: 400 }
        );
      }
    }

    // Update the plan
    const updatedPlan = await prisma.plan.update({
      where: { id: params.id },
      data: {
        ...(name && { name }),
        ...(price !== undefined && { price }),
        ...(features && { 
          features: typeof features === 'object' ? JSON.stringify(features) : features 
        }),
        ...(isActive !== undefined && { isActive }),
      },
    });

    // For SQLite development: parse features from string to JSON
    const parsedPlan = {
      ...updatedPlan,
      features: typeof updatedPlan.features === 'string' 
        ? JSON.parse(updatedPlan.features) 
        : updatedPlan.features
    };

    return NextResponse.json(parsedPlan);
  } catch (error) {
    console.error("Error updating plan:", error);
    return NextResponse.json(
      { error: "Failed to update plan" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/plans/[id] - Delete a plan
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  // Check if user is authorized (admin only)
  const authResult = await authorizeRequest(req, { requiredRole: "ADMIN" });
  const unauthorizedResponse = handleUnauthorized(authResult);
  if (unauthorizedResponse) return unauthorizedResponse;

  // Validate params
  const validation = validateParams(params, validationSchemas.plan.id);
  if (!validation.success) {
    return NextResponse.json({ error: validation.error }, { status: 400 });
  }

  try {
    // Check if plan exists
    const existingPlan = await prisma.plan.findUnique({
      where: { id: params.id },
      include: { subscriptions: true },
    });

    if (!existingPlan) {
      return NextResponse.json({ error: "Plan not found" }, { status: 404 });
    }

    // Check if plan has active subscriptions
    if (existingPlan.subscriptions.length > 0) {
      return NextResponse.json(
        { error: "Cannot delete a plan with active subscriptions" },
        { status: 400 }
      );
    }

    // Delete the plan
    await prisma.plan.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ message: "Plan deleted successfully" });
  } catch (error) {
    console.error("Error deleting plan:", error);
    return NextResponse.json(
      { error: "Failed to delete plan" },
      { status: 500 }
    );
  }
} 