import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { PrismaClient } from '@prisma/client';
import { authOptions } from '@/lib/auth';

const prisma = new PrismaClient();

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get chatbotId from query params
    const { searchParams } = new URL(req.url);
    const chatbotId = searchParams.get('chatbotId');

    if (!chatbotId) {
      return NextResponse.json({ error: 'Missing chatbotId parameter' }, { status: 400 });
    }

    // Verify that the user owns this chatbot
    const chatbot = await prisma.chatbot.findUnique({
      where: { id: chatbotId },
      include: { 
        user: true,
        structuredKbContact: true
      },
    });

    if (!chatbot || chatbot.user.email !== session.user.email) {
      return NextResponse.json({ error: 'Unauthorized access to chatbot' }, { status: 403 });
    }

    // If contact data exists, format it for the frontend
    let contactData = null;
    if (chatbot.structuredKbContact) {
      contactData = {
        fullAddress: chatbot.structuredKbContact.fullAddress || '',
        googleMapsLink: chatbot.structuredKbContact.googleMapsLink || '',
        howToReach: chatbot.structuredKbContact.howToReach || '',
        primaryPhone: chatbot.structuredKbContact.primaryPhone || '',
        whatsappNumber: chatbot.structuredKbContact.whatsappNumber || '',
        primaryEmail: chatbot.structuredKbContact.primaryEmail || '',
        websiteUrl: chatbot.structuredKbContact.websiteUrl || '',
        socialMediaLinks: chatbot.structuredKbContact.socialMediaLinks || [],
      };
    }

    return NextResponse.json({ contact: contactData });
  } catch (error) {
    console.error('Error fetching contact data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch contact data' },
      { status: 500 }
    );
  }
} 