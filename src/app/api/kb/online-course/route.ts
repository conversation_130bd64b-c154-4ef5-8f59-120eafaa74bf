import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth';

// Validation schema
const requestSchema = z.object({
  chatbotId: z.string(),
});

export async function GET(request: NextRequest) {
  try {
    // Get the current user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get chatbotId from query params
    const { searchParams } = new URL(request.url);
    const chatbotId = searchParams.get('chatbotId');

    // Validate request
    const validatedData = requestSchema.parse({ chatbotId });

    // Check if the user owns this chatbot
    const chatbot = await prisma.chatbot.findUnique({
      where: {
        id: validatedData.chatbotId,
        userId: user.id,
      },
    });

    if (!chatbot) {
      return NextResponse.json({ error: 'Chatbot not found' }, { status: 404 });
    }

    // Fetch online courses for this chatbot
    const onlineCourses = await prisma.onlineCourse.findMany({
      where: {
        chatbotId: validatedData.chatbotId,
      },
    });

    return NextResponse.json({ onlineCourses });
  } catch (error) {
    console.error('Error fetching online course data:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid request data', details: error.errors }, { status: 400 });
    }
    
    return NextResponse.json({ error: 'Failed to fetch online course data' }, { status: 500 });
  }
} 