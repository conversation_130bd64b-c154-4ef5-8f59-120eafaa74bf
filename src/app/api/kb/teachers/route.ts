import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { PrismaClient } from '@prisma/client';
import { authOptions } from '@/lib/auth';

const prisma = new PrismaClient();

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get chatbotId from query params
    const { searchParams } = new URL(req.url);
    const chatbotId = searchParams.get('chatbotId');

    if (!chatbotId) {
      return NextResponse.json({ error: 'Missing chatbotId parameter' }, { status: 400 });
    }

    // Verify that the user owns this chatbot
    const chatbot = await prisma.chatbot.findUnique({
      where: { id: chatbotId },
      include: { 
        user: true,
        structuredKbTeachers: true
      },
    });

    if (!chatbot || chatbot.user.email !== session.user.email) {
      return NextResponse.json({ error: 'Unauthorized access to chatbot' }, { status: 403 });
    }

    // Format teachers data for the frontend
    const teachersData = chatbot.structuredKbTeachers.map(teacher => ({
      id: teacher.id,
      name: teacher.name,
      role: teacher.role || '',
      photoUrl: teacher.photoUrl || '',
      bio: teacher.bio || '',
      certifications: teacher.certifications || [],
    }));

    return NextResponse.json({ teachers: teachersData });
  } catch (error) {
    console.error('Error fetching teachers data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch teachers data' },
      { status: 500 }
    );
  }
} 