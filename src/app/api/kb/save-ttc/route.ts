import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth';
import { publishJob } from '@/lib/qstash';

// Validation schemas
const priceOptionSchema = z.object({
  type: z.string(),
  price: z.number(),
  currency: z.string(),
});

const dateOptionSchema = z.object({
  start: z.string(),
  end: z.string(),
  status: z.string(),
});

const ttcSchema = z.object({
  id: z.string().optional(),
  name: z.string(),
  certificationBody: z.string().optional().nullable(),
  summary: z.string().optional().nullable(),
  duration: z.string().optional().nullable(),
  skillLevel: z.string().optional().nullable(),
  curriculumDetails: z.string().optional().nullable(),
  sampleDailySchedule: z.string().optional().nullable(),
  priceOptions: z.array(priceOptionSchema),
  inclusions: z.array(z.string()),
  exclusions: z.array(z.string()),
  upcomingDates: z.array(dateOptionSchema),
  applicationProcess: z.string().optional().nullable(),
});

const requestSchema = z.object({
  chatbotId: z.string(),
  ttcs: z.array(ttcSchema),
});

export async function POST(request: NextRequest) {
  try {
    // Get the current user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = requestSchema.parse(body);

    // Check if the user owns this chatbot
    const chatbot = await prisma.chatbot.findUnique({
      where: {
        id: validatedData.chatbotId,
        userId: user.id,
      },
    });

    if (!chatbot) {
      return NextResponse.json({ error: 'Chatbot not found' }, { status: 404 });
    }

    // Get existing TTCs to determine which ones to delete
    const existingTTCs = await prisma.tTC.findMany({
      where: {
        chatbotId: validatedData.chatbotId,
      },
      select: {
        id: true,
      },
    });

    const existingIds = existingTTCs.map(ttc => ttc.id);
    const newIds = validatedData.ttcs.filter(ttc => ttc.id).map(ttc => ttc.id as string);
    
    // IDs that exist in the database but not in the request should be deleted
    const idsToDelete = existingIds.filter(id => !newIds.includes(id));

    // Start a transaction to handle all database operations
    await prisma.$transaction(async (tx) => {
      // Delete TTCs that are no longer in the list
      if (idsToDelete.length > 0) {
        await tx.tTC.deleteMany({
          where: {
            id: {
              in: idsToDelete,
            },
          },
        });
      }

      // Update or create TTCs
      for (const ttc of validatedData.ttcs) {
        if (ttc.id) {
          // Update existing TTC
          await tx.tTC.update({
            where: {
              id: ttc.id,
            },
            data: {
              name: ttc.name,
              certificationBody: ttc.certificationBody,
              summary: ttc.summary,
              duration: ttc.duration,
              skillLevel: ttc.skillLevel,
              curriculumDetails: ttc.curriculumDetails,
              sampleDailySchedule: ttc.sampleDailySchedule,
              priceOptions: ttc.priceOptions,
              inclusions: ttc.inclusions,
              exclusions: ttc.exclusions,
              upcomingDates: ttc.upcomingDates,
              applicationProcess: ttc.applicationProcess,
            },
          });
        } else {
          // Create new TTC
          await tx.tTC.create({
            data: {
              chatbotId: validatedData.chatbotId,
              name: ttc.name,
              certificationBody: ttc.certificationBody,
              summary: ttc.summary,
              duration: ttc.duration,
              skillLevel: ttc.skillLevel,
              curriculumDetails: ttc.curriculumDetails,
              sampleDailySchedule: ttc.sampleDailySchedule,
              priceOptions: ttc.priceOptions,
              inclusions: ttc.inclusions,
              exclusions: ttc.exclusions,
              upcomingDates: ttc.upcomingDates,
              applicationProcess: ttc.applicationProcess,
            },
          });
        }
      }
    });

    // After creating or updating TTC data
    
    // Trigger background job to process structured data
    try {
      await publishJob(
        `${process.env.NEXTAUTH_URL}/api/kb/process-structured`,
        { chatbotId: validatedData.chatbotId }
      );
    } catch (error) {
      console.error('Failed to queue background job:', error);
      // Continue execution even if job queuing fails
    }

    return NextResponse.json({ 
      success: true,
      message: 'Teacher training courses saved successfully'
    });
  } catch (error) {
    console.error('Error saving TTC data:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid request data', details: error.errors }, { status: 400 });
    }
    
    return NextResponse.json({ error: 'Failed to save TTC data' }, { status: 500 });
  }
} 