import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { PrismaClient } from '@prisma/client';
import { authOptions } from '@/lib/auth';

const prisma = new PrismaClient();

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get chatbotId from query params
    const { searchParams } = new URL(req.url);
    const chatbotId = searchParams.get('chatbotId');

    if (!chatbotId) {
      return NextResponse.json({ error: 'Missing chatbotId parameter' }, { status: 400 });
    }

    // Verify that the user owns this chatbot
    const chatbot = await prisma.chatbot.findUnique({
      where: { id: chatbotId },
      include: { 
        user: true,
        structuredKbBrand: true
      },
    });

    if (!chatbot || chatbot.user.email !== session.user.email) {
      return NextResponse.json({ error: 'Unauthorized access to chatbot' }, { status: 403 });
    }

    // If brand data exists, format it for the frontend
    let brandData = null;
    if (chatbot.structuredKbBrand) {
      brandData = {
        schoolName: chatbot.structuredKbBrand.schoolName,
        tagline: chatbot.structuredKbBrand.tagline || '',
        schoolType: chatbot.structuredKbBrand.schoolType || '',
        yogaStylesTaught: chatbot.structuredKbBrand.yogaStylesTaught || [],
        missionStatement: chatbot.structuredKbBrand.missionStatement || '',
        aboutTheSchool: chatbot.structuredKbBrand.aboutTheSchool || '',
        founderInfo: chatbot.structuredKbBrand.founderInfo || '',
      };
    }

    return NextResponse.json({ brand: brandData });
  } catch (error) {
    console.error('Error fetching brand data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch brand data' },
      { status: 500 }
    );
  }
} 