import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth';
import { publishJob } from '@/lib/qstash';

// Validation schemas
const priceOptionSchema = z.object({
  type: z.string(),
  price: z.number(),
  currency: z.string(),
});

const dateOptionSchema = z.object({
  start: z.string(),
  end: z.string(),
  status: z.string(),
});

const retreatSchema = z.object({
  id: z.string().optional(),
  name: z.string(),
  theme: z.string().optional().nullable(),
  duration: z.string().optional().nullable(),
  yogaStyle: z.string().optional().nullable(),
  intendedAudience: z.string().optional().nullable(),
  highlights: z.array(z.string()),
  priceOptions: z.array(priceOptionSchema),
  upcomingDates: z.array(dateOptionSchema),
});

const requestSchema = z.object({
  chatbotId: z.string(),
  retreats: z.array(retreatSchema),
});

export async function POST(request: NextRequest) {
  try {
    // Get the current user
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const user = session.user;

    // Parse and validate request body
    const body = await request.json();
    const validatedData = requestSchema.parse(body);

    // Check if the user owns this chatbot
    const chatbot = await prisma.chatbot.findUnique({
      where: {
        id: validatedData.chatbotId,
        userId: user.id,
      },
    });

    if (!chatbot) {
      return NextResponse.json({ error: 'Chatbot not found' }, { status: 404 });
    }

    // Get existing retreats to determine which ones to delete
    const existingRetreats = await prisma.retreat.findMany({
      where: {
        chatbotId: validatedData.chatbotId,
      },
      select: {
        id: true,
      },
    });

    const existingIds = existingRetreats.map(retreat => retreat.id);
    const newIds = validatedData.retreats.filter(retreat => retreat.id).map(retreat => retreat.id as string);
    
    // IDs that exist in the database but not in the request should be deleted
    const idsToDelete = existingIds.filter(id => !newIds.includes(id));

    // Start a transaction to handle all database operations
    await prisma.$transaction(async (tx) => {
      // Delete retreats that are no longer in the list
      if (idsToDelete.length > 0) {
        await tx.retreat.deleteMany({
          where: {
            id: {
              in: idsToDelete,
            },
          },
        });
      }

      // Update or create retreats
      for (const retreat of validatedData.retreats) {
        if (retreat.id) {
          // Update existing retreat
          await tx.retreat.update({
            where: {
              id: retreat.id,
            },
            data: {
              name: retreat.name,
              theme: retreat.theme,
              duration: retreat.duration,
              intendedAudience: retreat.intendedAudience,
              highlights: retreat.highlights,
              priceOptions: retreat.priceOptions,
              upcomingDates: retreat.upcomingDates,
            },
          });
        } else {
          // Create new retreat
          await tx.retreat.create({
            data: {
              chatbotId: validatedData.chatbotId,
              name: retreat.name,
              theme: retreat.theme,
              duration: retreat.duration,
              intendedAudience: retreat.intendedAudience,
              highlights: retreat.highlights,
              priceOptions: retreat.priceOptions,
              upcomingDates: retreat.upcomingDates,
            },
          });
        }
      }
    });

    // After creating or updating retreat data
    
    // Trigger background job to process structured data
    try {
      await publishJob(
        `${process.env.NEXTAUTH_URL}/api/kb/process-structured`,
        { chatbotId: validatedData.chatbotId }
      );
    } catch (error) {
      console.error('Failed to queue background job:', error);
      // Continue execution even if job queuing fails
    }

    return NextResponse.json({ 
      success: true,
      message: 'Retreats saved successfully'
    });
  } catch (error) {
    console.error('Error saving retreat data:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid request data', details: error.errors }, { status: 400 });
    }
    
    return NextResponse.json({ error: 'Failed to save retreat data' }, { status: 500 });
  }
} 