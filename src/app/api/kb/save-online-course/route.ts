import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth';
import { publishJob } from '@/lib/qstash';

// Validation schemas
const onlineCourseSchema = z.object({
  id: z.string().optional(),
  name: z.string(),
  format: z.string().optional().nullable(),
  contentCovered: z.string().optional().nullable(),
  instructorName: z.string().optional().nullable(),
  accessType: z.string().optional().nullable(),
  price: z.number().optional().nullable(),
  currency: z.string().optional().nullable(),
  accessLink: z.string().optional().nullable(),
});

const requestSchema = z.object({
  chatbotId: z.string(),
  onlineCourses: z.array(onlineCourseSchema),
});

export async function POST(request: NextRequest) {
  try {
    // Get the current user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = requestSchema.parse(body);

    // Check if the user owns this chatbot
    const chatbot = await prisma.chatbot.findUnique({
      where: {
        id: validatedData.chatbotId,
        userId: user.id,
      },
    });

    if (!chatbot) {
      return NextResponse.json({ error: 'Chatbot not found' }, { status: 404 });
    }

    // Get existing online courses to determine which ones to delete
    const existingCourses = await prisma.onlineCourse.findMany({
      where: {
        chatbotId: validatedData.chatbotId,
      },
      select: {
        id: true,
      },
    });

    const existingIds = existingCourses.map(course => course.id);
    const newIds = validatedData.onlineCourses.filter(course => course.id).map(course => course.id as string);
    
    // IDs that exist in the database but not in the request should be deleted
    const idsToDelete = existingIds.filter(id => !newIds.includes(id));

    // Start a transaction to handle all database operations
    await prisma.$transaction(async (tx) => {
      // Delete online courses that are no longer in the list
      if (idsToDelete.length > 0) {
        await tx.onlineCourse.deleteMany({
          where: {
            id: {
              in: idsToDelete,
            },
          },
        });
      }

      // Update or create online courses
      for (const course of validatedData.onlineCourses) {
        if (course.id) {
          // Update existing online course
          await tx.onlineCourse.update({
            where: {
              id: course.id,
            },
            data: {
              name: course.name,
              format: course.format,
              contentCovered: course.contentCovered,
              instructorName: course.instructorName,
              accessType: course.accessType,
              price: course.price,
              currency: course.currency,
              accessLink: course.accessLink,
            },
          });
        } else {
          // Create new online course
          await tx.onlineCourse.create({
            data: {
              chatbotId: validatedData.chatbotId,
              name: course.name,
              format: course.format,
              contentCovered: course.contentCovered,
              instructorName: course.instructorName,
              accessType: course.accessType,
              price: course.price,
              currency: course.currency,
              accessLink: course.accessLink,
            },
          });
        }
      }
    });

    // After creating or updating online course data
    
    // Trigger background job to process structured data
    try {
      await publishJob(
        `${process.env.NEXTAUTH_URL}/api/kb/process-structured`,
        { chatbotId: validatedData.chatbotId }
      );
    } catch (error) {
      console.error('Failed to queue background job:', error);
      // Continue execution even if job queuing fails
    }

    return NextResponse.json({ 
      success: true,
      message: 'Online courses saved successfully'
    });
  } catch (error) {
    console.error('Error saving online course data:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid request data', details: error.errors }, { status: 400 });
    }
    
    return NextResponse.json({ error: 'Failed to save online course data' }, { status: 500 });
  }
} 