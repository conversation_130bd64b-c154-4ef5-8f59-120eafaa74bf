import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { PrismaClient } from '@prisma/client';
import { authOptions } from '@/lib/auth';
import { publishJob } from '@/lib/qstash';

const prisma = new PrismaClient();

// Input validation schema
const teacherSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Teacher name is required'),
  role: z.string().optional(),
  photoUrl: z.string().url().optional().or(z.literal('')),
  bio: z.string().optional(),
  certifications: z.array(z.string()).default([]),
});

const saveTeachersSchema = z.object({
  chatbotId: z.string().min(1, 'Chatbot ID is required'),
  teachers: z.array(teacherSchema),
});

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await req.json();
    const validationResult = saveTeachersSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { chatbotId, teachers } = validationResult.data;

    // Verify that the user owns this chatbot
    const chatbot = await prisma.chatbot.findUnique({
      where: { id: chatbotId },
      include: { 
        user: true,
        structuredKbTeachers: true
      },
    });

    if (!chatbot || chatbot.user.email !== session.user.email) {
      return NextResponse.json({ error: 'Unauthorized access to chatbot' }, { status: 403 });
    }

    // Delete existing teachers and create new ones
    await prisma.teacher.deleteMany({
      where: { chatbotId },
    });

    // Create new teachers
    await prisma.teacher.createMany({
      data: teachers.map(teacher => ({
        chatbotId,
        name: teacher.name,
        role: teacher.role,
        photoUrl: teacher.photoUrl,
        bio: teacher.bio,
        certifications: teacher.certifications || [],
      })),
    });

    // Trigger background job to process structured data
    try {
      await publishJob(
        `${process.env.NEXTAUTH_URL}/api/kb/process-structured`,
        { chatbotId }
      );
    } catch (error) {
      console.error('Failed to queue background job:', error);
      // Continue execution even if job queuing fails
    }

    return NextResponse.json({ 
      success: true,
      message: 'Teachers information saved successfully'
    });
  } catch (error) {
    console.error('Error saving teachers data:', error);
    return NextResponse.json(
      { error: 'Failed to save teachers data' },
      { status: 500 }
    );
  }
} 