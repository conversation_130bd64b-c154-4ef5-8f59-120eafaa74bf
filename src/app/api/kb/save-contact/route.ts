import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { PrismaClient } from '@prisma/client';
import { authOptions } from '@/lib/auth';
import { publishJob } from '@/lib/qstash';

const prisma = new PrismaClient();

// Input validation schema
const saveContactSchema = z.object({
  chatbotId: z.string().min(1, 'Chatbot ID is required'),
  contact: z.object({
    fullAddress: z.string().optional(),
    googleMapsLink: z.string().url().optional().or(z.literal('')),
    howToReach: z.string().optional(),
    primaryPhone: z.string().optional(),
    whatsappNumber: z.string().optional(),
    primaryEmail: z.string().email().optional().or(z.literal('')),
    websiteUrl: z.string().url().optional().or(z.literal('')),
    socialMediaLinks: z.array(
      z.object({
        platform: z.string(),
        url: z.string().url(),
      })
    ).default([]),
  }),
});

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await req.json();
    const validationResult = saveContactSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { chatbotId, contact } = validationResult.data;

    // Verify that the user owns this chatbot
    const chatbot = await prisma.chatbot.findUnique({
      where: { id: chatbotId },
      include: { user: true },
    });

    if (!chatbot || chatbot.user.email !== session.user.email) {
      return NextResponse.json({ error: 'Unauthorized access to chatbot' }, { status: 403 });
    }

    // Check if contact data already exists for this chatbot
    const existingContact = await prisma.schoolContact.findUnique({
      where: { chatbotId },
    });

    // Update or create contact data
    if (existingContact) {
      await prisma.schoolContact.update({
        where: { chatbotId },
        data: {
          fullAddress: contact.fullAddress,
          googleMapsLink: contact.googleMapsLink,
          howToReach: contact.howToReach,
          primaryPhone: contact.primaryPhone,
          whatsappNumber: contact.whatsappNumber,
          primaryEmail: contact.primaryEmail,
          websiteUrl: contact.websiteUrl,
          socialMediaLinks: contact.socialMediaLinks,
        },
      });
    } else {
      await prisma.schoolContact.create({
        data: {
          chatbotId,
          fullAddress: contact.fullAddress,
          googleMapsLink: contact.googleMapsLink,
          howToReach: contact.howToReach,
          primaryPhone: contact.primaryPhone,
          whatsappNumber: contact.whatsappNumber,
          primaryEmail: contact.primaryEmail,
          websiteUrl: contact.websiteUrl,
          socialMediaLinks: contact.socialMediaLinks,
        },
      });
    }

    // Trigger background job to process structured data
    try {
      await publishJob(
        `${process.env.NEXTAUTH_URL}/api/kb/process-structured`,
        { chatbotId }
      );
    } catch (error) {
      console.error('Failed to queue background job:', error);
      // Continue execution even if job queuing fails
    }

    // TODO: In a real implementation, we would trigger a background job here
    // to process all structured data into knowledge chunks with embeddings

    return NextResponse.json({ 
      success: true,
      message: 'School contact information saved successfully'
    });
  } catch (error) {
    console.error('Error saving contact data:', error);
    return NextResponse.json(
      { error: 'Failed to save contact data' },
      { status: 500 }
    );
  }
} 