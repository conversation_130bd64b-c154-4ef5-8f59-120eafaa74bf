import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth';
import { publishJob } from '@/lib/qstash';

// Validation schemas
const faqSchema = z.object({
  id: z.string().optional(),
  question: z.string(),
  answer: z.string(),
});

const requestSchema = z.object({
  chatbotId: z.string(),
  faqs: z.array(faqSchema),
});

export async function POST(request: NextRequest) {
  try {
    // Get the current user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = requestSchema.parse(body);

    // Check if the user owns this chatbot
    const chatbot = await prisma.chatbot.findUnique({
      where: {
        id: validatedData.chatbotId,
        userId: user.id,
      },
    });

    if (!chatbot) {
      return NextResponse.json({ error: 'Chatbot not found' }, { status: 404 });
    }

    // Get existing FAQs to determine which ones to delete
    const existingFaqs = await prisma.fAQ.findMany({
      where: {
        chatbotId: validatedData.chatbotId,
      },
      select: {
        id: true,
      },
    });

    const existingIds = existingFaqs.map(faq => faq.id);
    const newIds = validatedData.faqs.filter(faq => faq.id).map(faq => faq.id as string);
    
    // IDs that exist in the database but not in the request should be deleted
    const idsToDelete = existingIds.filter(id => !newIds.includes(id));

    // Start a transaction to handle all database operations
    await prisma.$transaction(async (tx) => {
      // Delete FAQs that are no longer in the list
      if (idsToDelete.length > 0) {
        await tx.fAQ.deleteMany({
          where: {
            id: {
              in: idsToDelete,
            },
          },
        });
      }

      // Update or create FAQs
      for (const faq of validatedData.faqs) {
        if (faq.id) {
          // Update existing FAQ
          await tx.fAQ.update({
            where: {
              id: faq.id,
            },
            data: {
              question: faq.question,
              answer: faq.answer,
            },
          });
        } else {
          // Create new FAQ
          await tx.fAQ.create({
            data: {
              chatbotId: validatedData.chatbotId,
              question: faq.question,
              answer: faq.answer,
            },
          });
        }
      }
    });

    // After creating or updating FAQ data
    
    // Trigger background job to process structured data
    try {
      await publishJob(
        `${process.env.NEXTAUTH_URL}/api/kb/process-structured`,
        { chatbotId: validatedData.chatbotId }
      );
    } catch (error) {
      console.error('Failed to queue background job:', error);
      // Continue execution even if job queuing fails
    }

    return NextResponse.json({ 
      success: true,
      message: 'FAQ information saved successfully'
    });
  } catch (error) {
    console.error('Error saving FAQ data:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid request data', details: error.errors }, { status: 400 });
    }
    
    return NextResponse.json({ error: 'Failed to save FAQ data' }, { status: 500 });
  }
} 