import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { PrismaClient } from '@prisma/client';
import { authOptions } from '@/lib/auth';
import { generateEmbedding } from '@/lib/embeddings';
import { publishJob } from '@/lib/qstash';

const prisma = new PrismaClient();

// Input validation schema
const saveSimpleKbSchema = z.object({
  text: z.string().min(1, 'Knowledge base text cannot be empty'),
  chatbotId: z.string().min(1, 'Chatbot ID is required'),
});

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await req.json();
    const validationResult = saveSimpleKbSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { text, chatbotId } = validationResult.data;

    // Verify that the user owns this chatbot
    const chatbot = await prisma.chatbot.findUnique({
      where: { id: chatbotId },
      include: { user: true },
    });

    if (!chatbot || chatbot.user.email !== session.user.email) {
      return NextResponse.json({ error: 'Unauthorized access to chatbot' }, { status: 403 });
    }

    // Save the simple KB text to the chatbot
    await prisma.chatbot.update({
      where: { id: chatbotId },
      data: { simpleKbText: text },
    });

    // Generate embeddings for the chunked text
    const chunks = await generateChunks(text);
    
    // Delete existing chunks
    await prisma.knowledgeBaseChunk.deleteMany({
      where: { chatbotId },
    });
    
    // Create new chunks with embeddings
    for (const chunk of chunks) {
      try {
        const embedding = await generateEmbedding(chunk);
        await prisma.knowledgeBaseChunk.create({
          data: {
            chatbotId,
            content: chunk,
            embedding,
            source: 'Simple KB',
          },
        });
      } catch (error) {
        console.error('Error creating chunk:', error);
      }
    }
    
    // Alternatively, we could trigger a background job to process the chunks
    // This would be more efficient for large amounts of text
    try {
      await publishJob(
        `${process.env.NEXTAUTH_URL}/api/kb/process-structured`,
        { chatbotId }
      );
    } catch (error) {
      console.error('Failed to queue background job:', error);
      // Continue execution even if job queuing fails
    }

    return NextResponse.json({
      success: true,
      message: `Successfully saved and processed ${chunks.length} chunks`,
    });
  } catch (error) {
    console.error('Error saving simple KB:', error);
    return NextResponse.json(
      { error: 'Failed to save knowledge base' },
      { status: 500 }
    );
  }
}

// Helper function to split text into chunks
function processTextIntoChunks(text: string): string[] {
  // Simple implementation: split by paragraphs and ensure chunks aren't too large
  const paragraphs = text.split(/\n\s*\n/);
  const chunks: string[] = [];
  
  // Maximum chunk size (roughly 1000 characters)
  const MAX_CHUNK_SIZE = 1000;
  
  let currentChunk = '';
  
  for (const paragraph of paragraphs) {
    // Skip empty paragraphs
    if (!paragraph.trim()) continue;
    
    // If adding this paragraph would make the chunk too large, save current chunk and start a new one
    if (currentChunk.length + paragraph.length > MAX_CHUNK_SIZE && currentChunk.length > 0) {
      chunks.push(currentChunk.trim());
      currentChunk = '';
    }
    
    // Add paragraph to current chunk
    currentChunk += paragraph + '\n\n';
    
    // If this single paragraph is larger than the max size, split it further
    if (paragraph.length > MAX_CHUNK_SIZE) {
      const sentences = paragraph.split(/(?<=[.!?])\s+/);
      let sentenceChunk = '';
      
      for (const sentence of sentences) {
        if (sentenceChunk.length + sentence.length > MAX_CHUNK_SIZE && sentenceChunk.length > 0) {
          chunks.push(sentenceChunk.trim());
          sentenceChunk = '';
        }
        
        sentenceChunk += sentence + ' ';
      }
      
      if (sentenceChunk.trim().length > 0) {
        chunks.push(sentenceChunk.trim());
      }
      
      // Reset current chunk since we've handled this large paragraph separately
      currentChunk = '';
    }
  }
  
  // Add the last chunk if it's not empty
  if (currentChunk.trim().length > 0) {
    chunks.push(currentChunk.trim());
  }
  
  return chunks;
} 