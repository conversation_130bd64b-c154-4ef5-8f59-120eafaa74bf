import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { PrismaClient } from '@prisma/client';
import { authOptions } from '@/lib/auth';
import { publishJob } from '@/lib/qstash';

const prisma = new PrismaClient();

// Input validation schema
const saveBrandSchema = z.object({
  chatbotId: z.string().min(1, 'Chatbot ID is required'),
  brand: z.object({
    schoolName: z.string().min(1, 'School name is required'),
    tagline: z.string().optional(),
    schoolType: z.string().optional(),
    yogaStylesTaught: z.array(z.string()).default([]),
    missionStatement: z.string().optional(),
    aboutTheSchool: z.string().optional(),
    founderInfo: z.string().optional(),
  }),
});

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await req.json();
    const validationResult = saveBrandSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { chatbotId, brand } = validationResult.data;

    // Verify that the user owns this chatbot
    const chatbot = await prisma.chatbot.findUnique({
      where: { id: chatbotId },
      include: { user: true },
    });

    if (!chatbot || chatbot.user.email !== session.user.email) {
      return NextResponse.json({ error: 'Unauthorized access to chatbot' }, { status: 403 });
    }

    // Check if brand data already exists for this chatbot
    const existingBrand = await prisma.schoolBrand.findUnique({
      where: { chatbotId },
    });

    // Update or create brand data
    if (existingBrand) {
      await prisma.schoolBrand.update({
        where: { chatbotId },
        data: {
          schoolName: brand.schoolName,
          tagline: brand.tagline,
          schoolType: brand.schoolType,
          yogaStylesTaught: brand.yogaStylesTaught,
          missionStatement: brand.missionStatement,
          aboutTheSchool: brand.aboutTheSchool,
          founderInfo: brand.founderInfo,
        },
      });
    } else {
      await prisma.schoolBrand.create({
        data: {
          chatbotId,
          schoolName: brand.schoolName,
          tagline: brand.tagline,
          schoolType: brand.schoolType,
          yogaStylesTaught: brand.yogaStylesTaught,
          missionStatement: brand.missionStatement,
          aboutTheSchool: brand.aboutTheSchool,
          founderInfo: brand.founderInfo,
        },
      });
    }

    // Trigger background job to process structured data
    try {
      await publishJob(
        `${process.env.NEXTAUTH_URL}/api/kb/process-structured`,
        { chatbotId }
      );
    } catch (error) {
      console.error('Failed to queue background job:', error);
      // Continue execution even if job queuing fails
    }

    return NextResponse.json({ 
      success: true,
      message: 'School brand information saved successfully'
    });
  } catch (error) {
    console.error('Error saving brand data:', error);
    return NextResponse.json(
      { error: 'Failed to save brand data' },
      { status: 500 }
    );
  }
} 