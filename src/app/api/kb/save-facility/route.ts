import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth';
import { publishJob } from '@/lib/qstash';

// Photo schema for the gallery
const photoSchema = z.object({
  url: z.string().url(),
  caption: z.string().optional(),
});

// Validation schema
const requestSchema = z.object({
  chatbotId: z.string(),
  facility: z.object({
    accommodationDetails: z.string().optional().nullable(),
    foodPhilosophy: z.string().optional().nullable(),
    otherAmenities: z.string().optional().nullable(),
    photoGallery: z.array(photoSchema).optional().nullable(),
    videoTourLink: z.string().url().optional().nullable(),
  }),
});

export async function POST(request: NextRequest) {
  try {
    // Get the current user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = requestSchema.parse(body);

    // Check if the user owns this chatbot
    const chatbot = await prisma.chatbot.findUnique({
      where: {
        id: validatedData.chatbotId,
        userId: user.id,
      },
    });

    if (!chatbot) {
      return NextResponse.json({ error: 'Chatbot not found' }, { status: 404 });
    }

    // Check if a facility record already exists for this chatbot
    const existingFacility = await prisma.facility.findUnique({
      where: {
        chatbotId: validatedData.chatbotId,
      },
    });

    if (existingFacility) {
      // Update existing facility record
      await prisma.facility.update({
        where: {
          id: existingFacility.id,
        },
        data: {
          accommodationDetails: validatedData.facility.accommodationDetails,
          foodPhilosophy: validatedData.facility.foodPhilosophy,
          otherAmenities: validatedData.facility.otherAmenities,
          photoGallery: validatedData.facility.photoGallery ? JSON.stringify(validatedData.facility.photoGallery) : null,
          videoTourLink: validatedData.facility.videoTourLink,
        },
      });
    } else {
      // Create new facility record
      await prisma.facility.create({
        data: {
          chatbotId: validatedData.chatbotId,
          accommodationDetails: validatedData.facility.accommodationDetails,
          foodPhilosophy: validatedData.facility.foodPhilosophy,
          otherAmenities: validatedData.facility.otherAmenities,
          photoGallery: validatedData.facility.photoGallery ? JSON.stringify(validatedData.facility.photoGallery) : null,
          videoTourLink: validatedData.facility.videoTourLink,
        },
      });
    }

    // After creating or updating facility data
    
    // Trigger background job to process structured data
    try {
      await publishJob(
        `${process.env.NEXTAUTH_URL}/api/kb/process-structured`,
        { chatbotId: validatedData.chatbotId }
      );
    } catch (error) {
      console.error('Failed to queue background job:', error);
      // Continue execution even if job queuing fails
    }

    return NextResponse.json({ 
      success: true,
      message: 'Facility information saved successfully'
    });
  } catch (error) {
    console.error('Error saving facility data:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid request data', details: error.errors }, { status: 400 });
    }
    
    return NextResponse.json({ error: 'Failed to save facility data' }, { status: 500 });
  }
} 