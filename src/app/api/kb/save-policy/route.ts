import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth';
import { publishJob } from '@/lib/qstash';

// Validation schema
const requestSchema = z.object({
  chatbotId: z.string(),
  policy: z.object({
    codeOfConduct: z.string().optional().nullable(),
    paymentPolicy: z.string().optional().nullable(),
    cancellationAndRefundPolicy: z.string().optional().nullable(),
    visaInformation: z.string().optional().nullable(),
    whatToBring: z.string().optional().nullable(),
  }),
});

export async function POST(request: NextRequest) {
  try {
    // Get the current user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = requestSchema.parse(body);

    // Check if the user owns this chatbot
    const chatbot = await prisma.chatbot.findUnique({
      where: {
        id: validatedData.chatbotId,
        userId: user.id,
      },
    });

    if (!chatbot) {
      return NextResponse.json({ error: 'Chatbot not found' }, { status: 404 });
    }

    // Check if a policy record already exists for this chatbot
    const existingPolicy = await prisma.policy.findUnique({
      where: {
        chatbotId: validatedData.chatbotId,
      },
    });

    if (existingPolicy) {
      // Update existing policy record
      await prisma.policy.update({
        where: {
          id: existingPolicy.id,
        },
        data: {
          codeOfConduct: validatedData.policy.codeOfConduct,
          paymentPolicy: validatedData.policy.paymentPolicy,
          cancellationAndRefundPolicy: validatedData.policy.cancellationAndRefundPolicy,
          visaInformation: validatedData.policy.visaInformation,
          whatToBring: validatedData.policy.whatToBring,
        },
      });
    } else {
      // Create new policy record
      await prisma.policy.create({
        data: {
          chatbotId: validatedData.chatbotId,
          codeOfConduct: validatedData.policy.codeOfConduct,
          paymentPolicy: validatedData.policy.paymentPolicy,
          cancellationAndRefundPolicy: validatedData.policy.cancellationAndRefundPolicy,
          visaInformation: validatedData.policy.visaInformation,
          whatToBring: validatedData.policy.whatToBring,
        },
      });
    }

    // After creating or updating policy data
    
    // Trigger background job to process structured data
    try {
      await publishJob(
        `${process.env.NEXTAUTH_URL}/api/kb/process-structured`,
        { chatbotId: validatedData.chatbotId }
      );
    } catch (error) {
      console.error('Failed to queue background job:', error);
      // Continue execution even if job queuing fails
    }

    return NextResponse.json({ 
      success: true,
      message: 'Policy information saved successfully'
    });
  } catch (error) {
    console.error('Error saving policy data:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid request data', details: error.errors }, { status: 400 });
    }
    
    return NextResponse.json({ error: 'Failed to save policy data' }, { status: 500 });
  }
} 