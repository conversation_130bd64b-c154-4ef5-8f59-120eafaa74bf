import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { generateEmbedding } from "@/lib/embeddings";
import { getServerSession } from "next-auth";

// This route will be called by QStash as a background job
export async function POST(req: NextRequest) {
  try {
    // Verify the request is from QStash using the signing key
    // In production, we would validate the QStash signature
    // For now, we'll proceed with the processing
    
    const body = await req.json();
    const { chatbotId } = body;
    
    if (!chatbotId) {
      return NextResponse.json({ error: "Missing chatbotId" }, { status: 400 });
    }
    
    // Fetch the chatbot and all its structured data
    const chatbot = await prisma.chatbot.findUnique({
      where: { id: chatbotId },
      include: {
        structuredKbBrand: true,
        structuredKbContact: true,
        structuredKbTeachers: true,
        structuredKbTtcs: true,
        structuredKbRetreats: true,
        structuredKbOnlineCourses: true,
        structuredKbFacility: true,
        structuredKbPolicies: true,
        structuredKbFaqs: true,
      },
    });
    
    if (!chatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 });
    }
    
    // Delete existing knowledge chunks for this chatbot
    await prisma.knowledgeBaseChunk.deleteMany({
      where: { chatbotId },
    });
    
    // Process and create new chunks
    const chunks: { content: string; source: string }[] = [];
    
    // Process School Brand
    if (chatbot.structuredKbBrand) {
      const { schoolName, tagline, schoolType, yogaStylesTaught, missionStatement, aboutTheSchool, founderInfo } = chatbot.structuredKbBrand;
      
      const brandContent = `
        School Name: ${schoolName}
        ${tagline ? `Tagline: ${tagline}` : ''}
        ${schoolType ? `School Type: ${schoolType}` : ''}
        ${yogaStylesTaught?.length ? `Yoga Styles Taught: ${yogaStylesTaught.join(', ')}` : ''}
        ${missionStatement ? `Mission Statement: ${missionStatement}` : ''}
        ${aboutTheSchool ? `About The School: ${aboutTheSchool}` : ''}
        ${founderInfo ? `Founder Information: ${founderInfo}` : ''}
      `.trim();
      
      chunks.push({
        content: brandContent,
        source: "School Brand",
      });
    }
    
    // Process School Contact
    if (chatbot.structuredKbContact) {
      const { fullAddress, googleMapsLink, howToReach, primaryPhone, whatsappNumber, primaryEmail, websiteUrl, socialMediaLinks } = chatbot.structuredKbContact;
      
      const contactContent = `
        ${fullAddress ? `Address: ${fullAddress}` : ''}
        ${googleMapsLink ? `Google Maps: ${googleMapsLink}` : ''}
        ${howToReach ? `How to Reach: ${howToReach}` : ''}
        ${primaryPhone ? `Phone: ${primaryPhone}` : ''}
        ${whatsappNumber ? `WhatsApp: ${whatsappNumber}` : ''}
        ${primaryEmail ? `Email: ${primaryEmail}` : ''}
        ${websiteUrl ? `Website: ${websiteUrl}` : ''}
        ${socialMediaLinks ? `Social Media: ${JSON.stringify(socialMediaLinks)}` : ''}
      `.trim();
      
      chunks.push({
        content: contactContent,
        source: "School Contact",
      });
    }
    
    // Process Teachers
    if (chatbot.structuredKbTeachers.length) {
      chatbot.structuredKbTeachers.forEach((teacher) => {
        const { name, role, photoUrl, bio, certifications } = teacher;
        
        const teacherContent = `
          Teacher Name: ${name}
          ${role ? `Role: ${role}` : ''}
          ${bio ? `Biography: ${bio}` : ''}
          ${certifications?.length ? `Certifications: ${certifications.join(', ')}` : ''}
        `.trim();
        
        chunks.push({
          content: teacherContent,
          source: `Teacher: ${name}`,
        });
      });
    }
    
    // Process TTCs
    if (chatbot.structuredKbTtcs.length) {
      chatbot.structuredKbTtcs.forEach((ttc) => {
        const { 
          name, certificationBody, summary, duration, skillLevel, 
          curriculumDetails, sampleDailySchedule, priceOptions,
          inclusions, exclusions, upcomingDates, applicationProcess
        } = ttc;
        
        const ttcContent = `
          Course Name: ${name}
          ${certificationBody ? `Certification Body: ${certificationBody}` : ''}
          ${summary ? `Summary: ${summary}` : ''}
          ${duration ? `Duration: ${duration}` : ''}
          ${skillLevel ? `Skill Level: ${skillLevel}` : ''}
          ${curriculumDetails ? `Curriculum Details: ${curriculumDetails}` : ''}
          ${sampleDailySchedule ? `Sample Daily Schedule: ${sampleDailySchedule}` : ''}
          ${priceOptions ? `Price Options: ${JSON.stringify(priceOptions)}` : ''}
          ${inclusions?.length ? `Inclusions: ${inclusions.join(', ')}` : ''}
          ${exclusions?.length ? `Exclusions: ${exclusions.join(', ')}` : ''}
          ${upcomingDates ? `Upcoming Dates: ${JSON.stringify(upcomingDates)}` : ''}
          ${applicationProcess ? `Application Process: ${applicationProcess}` : ''}
        `.trim();
        
        chunks.push({
          content: ttcContent,
          source: `TTC: ${name}`,
        });
      });
    }
    
    // Process Retreats
    if (chatbot.structuredKbRetreats.length) {
      chatbot.structuredKbRetreats.forEach((retreat) => {
        const { name, theme, duration, intendedAudience, highlights, priceOptions, upcomingDates } = retreat;
        
        const retreatContent = `
          Retreat Name: ${name}
          ${theme ? `Theme: ${theme}` : ''}
          ${duration ? `Duration: ${duration}` : ''}
          ${intendedAudience ? `Intended Audience: ${intendedAudience}` : ''}
          ${highlights?.length ? `Highlights: ${highlights.join(', ')}` : ''}
          ${priceOptions ? `Price Options: ${JSON.stringify(priceOptions)}` : ''}
          ${upcomingDates ? `Upcoming Dates: ${JSON.stringify(upcomingDates)}` : ''}
        `.trim();
        
        chunks.push({
          content: retreatContent,
          source: `Retreat: ${name}`,
        });
      });
    }
    
    // Process Online Courses
    if (chatbot.structuredKbOnlineCourses.length) {
      chatbot.structuredKbOnlineCourses.forEach((course) => {
        const { name, format, contentCovered, instructorName, accessType, price, currency, accessLink } = course;
        
        const courseContent = `
          Course Name: ${name}
          ${format ? `Format: ${format}` : ''}
          ${contentCovered ? `Content Covered: ${contentCovered}` : ''}
          ${instructorName ? `Instructor: ${instructorName}` : ''}
          ${accessType ? `Access Type: ${accessType}` : ''}
          ${price ? `Price: ${price} ${currency || ''}` : ''}
          ${accessLink ? `Access Link: ${accessLink}` : ''}
        `.trim();
        
        chunks.push({
          content: courseContent,
          source: `Online Course: ${name}`,
        });
      });
    }
    
    // Process Facility
    if (chatbot.structuredKbFacility) {
      const { accommodationDetails, foodPhilosophy, otherAmenities, photoGallery, videoTourLink } = chatbot.structuredKbFacility;
      
      const facilityContent = `
        ${accommodationDetails ? `Accommodation Details: ${accommodationDetails}` : ''}
        ${foodPhilosophy ? `Food Philosophy: ${foodPhilosophy}` : ''}
        ${otherAmenities ? `Other Amenities: ${otherAmenities}` : ''}
        ${videoTourLink ? `Video Tour Link: ${videoTourLink}` : ''}
      `.trim();
      
      chunks.push({
        content: facilityContent,
        source: "Facility",
      });
    }
    
    // Process Policies
    if (chatbot.structuredKbPolicies) {
      const { codeOfConduct, paymentPolicy, cancellationAndRefundPolicy, visaInformation, whatToBring } = chatbot.structuredKbPolicies;
      
      const policiesContent = `
        ${codeOfConduct ? `Code of Conduct: ${codeOfConduct}` : ''}
        ${paymentPolicy ? `Payment Policy: ${paymentPolicy}` : ''}
        ${cancellationAndRefundPolicy ? `Cancellation and Refund Policy: ${cancellationAndRefundPolicy}` : ''}
        ${visaInformation ? `Visa Information: ${visaInformation}` : ''}
        ${whatToBring ? `What to Bring: ${whatToBring}` : ''}
      `.trim();
      
      chunks.push({
        content: policiesContent,
        source: "Policies",
      });
    }
    
    // Process FAQs
    if (chatbot.structuredKbFaqs.length) {
      chatbot.structuredKbFaqs.forEach((faq) => {
        const { question, answer } = faq;
        
        const faqContent = `
          Question: ${question}
          Answer: ${answer}
        `.trim();
        
        chunks.push({
          content: faqContent,
          source: `FAQ: ${question}`,
        });
      });
    }
    
    // Generate embeddings and save chunks
    const createdChunks = [];
    for (const chunk of chunks) {
      try {
        const embedding = await generateEmbedding(chunk.content);
        const createdChunk = await prisma.knowledgeBaseChunk.create({
          data: {
            chatbotId,
            content: chunk.content,
            embedding,
            source: chunk.source,
          },
        });
        createdChunks.push(createdChunk);
      } catch (error) {
        console.error(`Error processing chunk ${chunk.source}:`, error);
      }
    }
    
    return NextResponse.json({
      success: true,
      message: `Processed ${createdChunks.length} chunks for chatbot ${chatbotId}`,
    });
    
  } catch (error) {
    console.error("Error processing structured KB:", error);
    return NextResponse.json({ error: "Failed to process structured KB" }, { status: 500 });
  }
} 