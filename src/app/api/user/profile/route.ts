import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { authorizeRequest, handleUnauthorized } from "@/lib/auth";

// GET /api/user/profile - Get the current user's profile
export async function GET(req: NextRequest) {
  // Check if user is authorized
  const authResult = await authorizeRequest(req);
  const unauthorizedResponse = handleUnauthorized(authResult);
  if (unauthorizedResponse) return unauthorizedResponse;

  const { userId } = authResult;

  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        subscription: {
          select: {
            id: true,
            status: true,
            currentPeriodEnd: true,
            tokensUsedThisPeriod: true,
            sessionsThisPeriod: true,
            plan: {
              select: {
                id: true,
                name: true,
                price: true,
                features: true,
              },
            },
          },
        },
        chatbot: {
          select: {
            id: true,
            approvedDomain: true,
            systemPrompt: true,
            llmProvider: true,
            llmModel: true,
            widgetConfig: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // For SQLite development: parse features from string to JSON
    if (user.subscription && typeof user.subscription.plan.features === 'string') {
      user.subscription.plan.features = JSON.parse(user.subscription.plan.features);
    }

    // For SQLite development: parse widgetConfig from string to JSON
    if (user.chatbot && typeof user.chatbot.widgetConfig === 'string') {
      user.chatbot.widgetConfig = JSON.parse(user.chatbot.widgetConfig);
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error("Error fetching user profile:", error);
    return NextResponse.json(
      { error: "Failed to fetch user profile" },
      { status: 500 }
    );
  }
} 