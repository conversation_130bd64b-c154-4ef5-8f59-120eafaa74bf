import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { generateEmbedding } from '@/lib/embeddings';
import { v4 as uuidv4 } from 'uuid';

// Input validation schema
const sendMessageSchema = z.object({
  botId: z.string().min(1, 'Bot ID is required'),
  visitorId: z.string().min(1, 'Visitor ID is required'),
  sessionId: z.string().optional(),
  message: z.string().min(1, 'Message is required'),
});

// Mock LLM function (to be replaced with actual implementation)
async function callLLM(prompt: string): Promise<string> {
  // In a real implementation, this would call an LLM API
  // For now, we'll return a mock response
  await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
  return `This is a mock response to "${prompt}". In a real implementation, this would be generated by an LLM.`;
}

export async function POST(req: NextRequest) {
  try {
    // Parse and validate request body
    const body = await req.json();
    const validationResult = sendMessageSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validationResult.error.errors },
        { status: 400 }
      );
    }
    
    const { botId, visitorId, sessionId, message } = validationResult.data;
    
    // Fetch the chatbot to verify it exists and check subscription status
    const chatbot = await prisma.chatbot.findUnique({
      where: { id: botId },
      include: {
        user: {
          include: {
            subscription: true,
          },
        },
      },
    });
    
    if (!chatbot) {
      return NextResponse.json({ error: 'Chatbot not found' }, { status: 404 });
    }
    
    // Check subscription status and usage limits
    const subscription = chatbot.user?.subscription;
    
    if (!subscription || subscription.status !== 'active' || new Date(subscription.currentPeriodEnd) < new Date()) {
      return NextResponse.json({ error: 'Subscription inactive' }, { status: 403 });
    }
    
    const plan = await prisma.plan.findUnique({
      where: { id: subscription.planId },
    });
    
    if (!plan) {
      return NextResponse.json({ error: 'Plan not found' }, { status: 500 });
    }
    
    // Parse plan features
    const planFeatures = plan.features as { tokenLimit: number, kbType: 'simple' | 'structured' };
    
    // Check token usage
    if (subscription.tokensUsedThisPeriod >= planFeatures.tokenLimit) {
      return NextResponse.json({ error: 'Token limit exceeded' }, { status: 403 });
    }
    
    // Find or create visitor
    let visitor = await prisma.visitor.findUnique({
      where: { id: visitorId },
    });
    
    if (!visitor) {
      visitor = await prisma.visitor.create({
        data: { id: visitorId },
      });
    }
    
    // Find or create chat session
    let chatSession;
    
    if (sessionId) {
      chatSession = await prisma.chatSession.findUnique({
        where: { id: sessionId },
        include: { messages: true },
      });
      
      if (!chatSession || chatSession.chatbotId !== botId || chatSession.visitorId !== visitorId) {
        return NextResponse.json({ error: 'Invalid session ID' }, { status: 400 });
      }
    } else {
      // Create a new chat session
      chatSession = await prisma.chatSession.create({
        data: {
          chatbotId: botId,
          visitorId: visitorId,
          ablyChannel: `chat:${botId}:${uuidv4()}`,
          controller: 'LLM',
        },
        include: { messages: true },
      });
    }
    
    // Save the visitor message
    const visitorMessage = await prisma.message.create({
      data: {
        chatSessionId: chatSession.id,
        senderType: 'VISITOR',
        content: message,
      },
    });
    
    // Check if session is controlled by LLM or human
    if (chatSession.controller === 'USER') {
      // If controlled by human, just save the message and return
      return NextResponse.json({
        success: true,
        sessionId: chatSession.id,
        message: 'Message received. An agent will respond shortly.',
        waitingForHuman: true,
      });
    }
    
    // Build the prompt for the LLM
    let prompt = '';
    
    // 1. Add system prompt
    prompt += chatbot.systemPrompt || 
      'You are YogaBot, a helpful assistant for a yoga school. Answer questions based on the provided context.';
    
    // 2. Add knowledge base context
    // For structured KB, perform semantic search to find relevant chunks
    if (planFeatures.kbType === 'structured') {
      try {
        const embedding = await generateEmbedding(message);
        
        // Fetch relevant knowledge chunks
        const knowledgeChunks = await prisma.$queryRaw`
          SELECT id, content, source
          FROM "KnowledgeBaseChunk"
          WHERE "chatbotId" = ${botId}
          ORDER BY embedding <-> ${embedding}::vector
          LIMIT 5
        `;
        
        if (Array.isArray(knowledgeChunks) && knowledgeChunks.length > 0) {
          prompt += '\n\nContext from knowledge base:\n';
          
          for (const chunk of knowledgeChunks) {
            prompt += `\n--- ${chunk.source} ---\n${chunk.content}\n`;
          }
        }
      } catch (error) {
        console.error('Error performing semantic search:', error);
      }
    } else if (chatbot.simpleKbText) {
      // For simple KB, just use the raw text
      prompt += `\n\nContext from knowledge base:\n${chatbot.simpleKbText}`;
    }
    
    // 3. Add chat history (last 5 messages)
    const recentMessages = chatSession.messages
      .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
      .slice(-5);
    
    if (recentMessages.length > 0) {
      prompt += '\n\nChat history:\n';
      
      for (const msg of recentMessages) {
        const role = msg.senderType === 'VISITOR' ? 'User' : 'Assistant';
        prompt += `${role}: ${msg.content}\n`;
      }
    }
    
    // 4. Add the current user message
    prompt += `\n\nUser: ${message}\n\nAssistant:`;
    
    // Call LLM to generate response
    const response = await callLLM(prompt);
    
    // Save the LLM response
    const botMessage = await prisma.message.create({
      data: {
        chatSessionId: chatSession.id,
        senderType: 'LLM',
        content: response,
        systemData: { prompt },
      },
    });
    
    // Update token count (mock for now)
    const estimatedTokens = Math.ceil((prompt.length + response.length) / 4);
    
    await prisma.chatSession.update({
      where: { id: chatSession.id },
      data: { tokenCount: { increment: estimatedTokens } },
    });
    
    await prisma.subscription.update({
      where: { id: subscription.id },
      data: { tokensUsedThisPeriod: { increment: estimatedTokens } },
    });
    
    return NextResponse.json({
      success: true,
      sessionId: chatSession.id,
      message: response,
    });
  } catch (error) {
    console.error('Error processing message:', error);
    return NextResponse.json(
      { error: 'Failed to process message' },
      { status: 500 }
    );
  }
} 