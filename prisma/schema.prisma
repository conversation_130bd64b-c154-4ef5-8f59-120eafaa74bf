generator client {
  provider        = "prisma-client-js"
  output          = "../src/generated/prisma"
  previewFeatures = ["postgresqlExtensions"]
}

datasource db {
  provider   = "postgresql"
  url        = env("POSTGRES_PRISMA_URL")
  extensions = [vector]
}

model User {
  id           String        @id @default(cuid())
  email        String        @unique
  name         String?
  password     String
  role         Role          @default(USER)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  chatbot      Chatbot?
  subscription Subscription?
}

model Plan {
  id            String         @id @default(cuid())
  name          String         @unique
  price         Int
  features      Json
  isActive      Boolean        @default(true)
  subscriptions Subscription[]
}

model Subscription {
  id                     String   @id @default(cuid())
  userId                 String   @unique
  planId                 String
  status                 String
  currentPeriodEnd       DateTime
  razorpaySubscriptionId String?  @unique
  tokensUsedThisPeriod   Int      @default(0)
  sessionsThisPeriod     Int      @default(0)
  plan                   Plan     @relation(fields: [planId], references: [id])
  user                   User     @relation(fields: [userId], references: [id])
}

model Chatbot {
  id                        String               @id @default(cuid())
  userId                    String               @unique
  approvedDomain            String
  systemPrompt              String?
  llmProvider               String               @default("gemini")
  llmModel                  String               @default("gemini-pro")
  encryptedLlmApiKey        String?
  widgetConfig              Json?
  smtpConfig                Json?
  simpleKbText              String?
  chatSessions              ChatSession[]
  user                      User                 @relation(fields: [userId], references: [id])
  structuredKbFaqs          FAQ[]
  structuredKbFacility      Facility?
  knowledgeChunks           KnowledgeBaseChunk[]
  structuredKbOnlineCourses OnlineCourse[]
  personas                  Persona[]
  structuredKbPolicies      Policy?
  structuredKbRetreats      Retreat[]
  structuredKbBrand         SchoolBrand?
  structuredKbContact       SchoolContact?
  structuredKbTtcs          TTC[]
  structuredKbTeachers      Teacher[]
}

model Persona {
  id          String  @id @default(cuid())
  chatbotId   String
  name        String
  personaText String
  isActive    Boolean @default(true)
  chatbot     Chatbot @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
}

model KnowledgeBaseChunk {
  id        String                @id @default(cuid())
  chatbotId String
  content   String
  embedding Unsupported("vector")
  source    String?
  chatbot   Chatbot               @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
}

model SchoolBrand {
  id               String   @id @default(cuid())
  chatbotId        String   @unique
  schoolName       String
  tagline          String?
  schoolType       String?
  yogaStylesTaught String[]
  missionStatement String?
  aboutTheSchool   String?
  founderInfo      String?
  chatbot          Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
}

model SchoolContact {
  id               String  @id @default(cuid())
  chatbotId        String  @unique
  fullAddress      String?
  googleMapsLink   String?
  howToReach       String?
  primaryPhone     String?
  whatsappNumber   String?
  primaryEmail     String?
  websiteUrl       String?
  socialMediaLinks Json?
  chatbot          Chatbot @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
}

model Teacher {
  id             String   @id @default(cuid())
  chatbotId      String
  name           String
  role           String?
  photoUrl       String?
  bio            String?
  certifications String[]
  chatbot        Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
}

model TTC {
  id                  String   @id @default(cuid())
  chatbotId           String
  name                String
  certificationBody   String?
  summary             String?
  duration            String?
  skillLevel          String?
  curriculumDetails   String?
  sampleDailySchedule String?
  priceOptions        Json
  inclusions          String[]
  exclusions          String[]
  upcomingDates       Json
  applicationProcess  String?
  chatbot             Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
}

model Retreat {
  id               String   @id @default(cuid())
  chatbotId        String
  name             String
  theme            String?
  duration         String?
  intendedAudience String?
  highlights       String[]
  priceOptions     Json
  upcomingDates    Json
  chatbot          Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
}

model OnlineCourse {
  id             String  @id @default(cuid())
  chatbotId      String
  name           String
  format         String?
  contentCovered String?
  instructorName String?
  accessType     String?
  price          Float?
  currency       String?
  accessLink     String?
  chatbot        Chatbot @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
}

model Facility {
  id                   String  @id @default(cuid())
  chatbotId            String  @unique
  accommodationDetails String?
  foodPhilosophy       String?
  otherAmenities       String?
  photoGallery         Json?
  videoTourLink        String?
  chatbot              Chatbot @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
}

model Policy {
  id                          String  @id @default(cuid())
  chatbotId                   String  @unique
  codeOfConduct               String?
  paymentPolicy               String?
  cancellationAndRefundPolicy String?
  visaInformation             String?
  whatToBring                 String?
  chatbot                     Chatbot @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
}

model FAQ {
  id        String  @id @default(cuid())
  chatbotId String
  question  String
  answer    String
  chatbot   Chatbot @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
}

model ChatSession {
  id          String    @id @default(cuid())
  chatbotId   String
  visitorId   String
  controller  String    @default("LLM")
  ablyChannel String    @unique
  tokenCount  Int       @default(0)
  createdAt   DateTime  @default(now())
  chatbot     Chatbot   @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  visitor     Visitor   @relation(fields: [visitorId], references: [id])
  messages    Message[]
}

model Message {
  id            String      @id @default(cuid())
  chatSessionId String
  senderType    String
  content       String
  systemData    Json?
  createdAt     DateTime    @default(now())
  chatSession   ChatSession @relation(fields: [chatSessionId], references: [id], onDelete: Cascade)
}

model Visitor {
  id           String        @id @default(cuid())
  email        String?
  name         String?
  profileData  Json?
  chatSessions ChatSession[]
}

enum Role {
  ADMIN
  USER
}
