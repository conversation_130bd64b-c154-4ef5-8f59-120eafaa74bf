### **Project YogaBot Live: Complete Project Specification & Development Plan**

**Document Version:** 1.0 (SaaS Edition)
**Date:** June 15, 2025
**Purpose:** This document provides the complete technical specification and phased development plan for the YogaBot Live SaaS platform. It is the single **Source Of Truth** for the AI Developer, outlining the full architecture, data models, feature implementation, and deployment strategy.

---

### **Part 1: Project Specification (PRD)**

#### **1.1. Project Vision & Architecture Overview**

YogaBot Live is a multi-tenant, full-stack B2B SaaS platform designed to provide yoga schools (Users) with a plan-based, AI-powered chatbot. The platform's core mission is to automate visitor engagement, handle inquiries 24/7, and provide a deeply personalized experience that reflects the unique brand of each yoga school, with capabilities governed by subscription tiers.

The application will be built as a **monolithic Next.js application** using the App Router. This serverless-first architecture is designed for optimal performance, infinite scalability, and cost-effectiveness. The core business logic will revolve around a robust `Plan` and `Subscription` system, controlling feature access and usage limits for every user.

#### **1.2. Core Technical Stack & Services**

(The technical stack remains the same as it is robust and suitable for the expanded scope.)

| Component | Technology / Service | Rationale |
| :--- | :--- | :--- |
| **Framework** | **Next.js 14+ (App Router, TypeScript)** | Provides a full-stack environment. Server Components for performance, and TypeScript for robust type safety. |
| **Hosting & Compute** | **Vercel** | Seamless CI/CD via GitHub. Serverless Functions for scalable backend logic. |
| **Database** | **Vercel Postgres** | Fully managed PostgreSQL with native `pgvector` extension support. |
| **ORM (Database Client)** | **Prisma** | Type-safe, elegant API for all database interactions. |
| **Authentication** | **NextAuth.js** | Complete, secure, and configurable authentication solution for Next.js. |
| **Security/Validation** | **bcryptjs, zod, Node.js crypto** | For password hashing, schema-based input validation, and data encryption. |
| **Real-Time Engine**| **Ably** | Manages persistent WebSocket connections for live chat and notifications. |
| **Background Jobs/Queue**| **Upstash QStash** | Serverless queue and scheduling service for asynchronous tasks. |
| **Email Sending** | **Nodemailer** | Robust Node.js library for sending emails via user-configured SMTP. |
| **Embedding Model** | **`Xenova/transformers.js`** | Enables free, self-contained generation of text embeddings within Serverless Functions. |

#### **1.3. System Personas & User Journeys**

*   **The Admin (Platform Owner):** The superuser with complete control.
    *   **Journey:** Logs into `app.yogabot.live/admin`. Creates and manages subscription `Plans`. Creates `User` accounts and assigns them a `Chatbot` and `Subscription`. Can override any `Chatbot` setting, including its system prompt and LLM configuration, for fine-tuning and support.

*   **The User (Yoga School):** The client whose experience is defined by their subscription `Plan`.
    *   **Journey:** Logs into `app.yogabot.live/dashboard`. Their dashboard's capabilities (e.g., KB type, feature access) are determined by their plan. They manage their chatbot's Knowledge Base (either via the Structured Form or Simple Text), Personas, and settings. They monitor chats and can take over from the LLM.

*   **The Visitor (Website End-User):** The end-user who interacts with the chatbot.
    *   **Journey:** Interacts with the embedded `<iframe>` chat widget on a User's website. The AI's responses are generated based on the specific Knowledge Base and configuration set by the yoga school.

#### **1.4. Database Schema & Data Models (Prisma)**

This schema is completely redesigned to support the plan-based SaaS architecture and the structured knowledge base.

```prisma
// This is your Prisma schema file.

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("POSTGRES_PRISMA_URL")
}

// ===================================
// 1. Core SaaS & User Models
// ===================================

model User {
  id           String        @id @default(cuid())
  email        String        @unique
  name         String?
  password     String        // Stores a secure hash from bcrypt
  role         Role          @default(USER)
  chatbot      Chatbot?
  subscription Subscription?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum Role {
  ADMIN
  USER
}

model Plan {
  id        String   @id @default(cuid())
  name      String   @unique // e.g., "Basic", "Pro", "Premium"
  price     Int      // Price in cents
  features  Json     // Defines capabilities: { kbType: "simple" | "structured", tokenLimit: 50000, canUseBYOK: false, ... }
  isActive  Boolean  @default(true)

  subscriptions Subscription[]
}

model Subscription {
  id                     String    @id @default(cuid())
  userId                 String    @unique
  user                   User      @relation(fields: [userId], references: [id])
  planId                 String
  plan                   Plan      @relation(fields: [planId], references: [id])
  status                 String    // e.g., "active", "cancelled", "past_due"
  currentPeriodEnd       DateTime
  razorpaySubscriptionId String?   @unique
  
  // Usage Tracking
  tokensUsedThisPeriod   Int       @default(0)
  sessionsThisPeriod     Int       @default(0)
}

// ===================================
// 2. Chatbot & Prompt Configuration
// ===================================

model Chatbot {
  id                    String    @id @default(cuid())
  userId                String    @unique
  user                  User      @relation(fields: [userId], references: [id])
  approvedDomain        String
  
  // Admin-controlled LLM settings
  systemPrompt          String?   @db.Text
  llmProvider           String    @default("gemini")
  llmModel              String    @default("gemini-pro")
  encryptedLlmApiKey    String?   // For BYOK (Bring Your Own Key)

  // User-controlled settings
  widgetConfig          Json?
  smtpConfig            Json?     // Encrypted SMTP credentials
  personas              Persona[]
  
  // Knowledge Base Data
  simpleKbText          String?   @db.Text // For "simple" plan users
  structuredKbBrand     SchoolBrand?
  structuredKbContact   SchoolContact?
  structuredKbTeachers  Teacher[]
  structuredKbTtcs      TTC[]
  structuredKbRetreats  Retreat[]
  structuredKbPolicies  Policy[]
  structuredKbFaqs      FAQ[]

  // Searchable Data & History
  knowledgeChunks       KnowledgeBaseChunk[]
  chatSessions          ChatSession[]
}

model Persona {
  id          String   @id @default(cuid())
  chatbotId   String
  chatbot     Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  name        String
  personaText String   @db.Text
  isActive    Boolean  @default(true)
}

// This model holds the final, searchable data generated from EITHER the simple OR structured KB.
model KnowledgeBaseChunk {
  id        String   @id @default(cuid())
  chatbotId String
  chatbot   Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  content   String   @db.Text
  embedding vector(384)
  source    String?  // e.g., "TTC: 200-Hour Foundational" to identify the source
}

// ===================================
// 3. Structured Knowledge Base Models
// (Mirrors the structure of form.md)
// ===================================

model SchoolBrand {
  id              String   @id @default(cuid())
  chatbotId       String   @unique
  chatbot         Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  schoolName      String
  tagline         String?
  schoolType      String?
  yogaStylesTaught String[]
  missionStatement String?   @db.Text
  aboutTheSchool  String?   @db.Text
  founderInfo     String?   @db.Text
}

model SchoolContact {
  id              String   @id @default(cuid())
  chatbotId       String   @unique
  chatbot         Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  fullAddress     String?
  googleMapsLink  String?
  howToReach      String?   @db.Text
  primaryPhone    String?
  whatsappNumber  String?
  primaryEmail    String?
  websiteUrl      String?
  socialMediaLinks Json?     // [{ platform: 'Instagram', url: '...' }]
}

model Teacher {
  id              String   @id @default(cuid())
  chatbotId       String
  chatbot         Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  name            String
  role            String?
  photoUrl        String?
  bio             String?   @db.Text
  certifications  String[]
}

model TTC {
  id                  String   @id @default(cuid())
  chatbotId           String
  chatbot             Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  name                String
  certificationBody   String?
  summary             String?   @db.Text
  duration            String?
  skillLevel          String?
  curriculumDetails   String?   @db.Text
  sampleDailySchedule String?   @db.Text
  priceOptions        Json     // [{ type: 'Shared Twin', price: 1800 }, ...]
  inclusions          String[]
  exclusions          String[]
  upcomingDates       Json     // [{ start: '...', end: '...', status: 'Open' }]
  applicationProcess  String?   @db.Text
}

model Retreat {
  id                  String   @id @default(cuid())
  chatbotId           String
  chatbot             Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  name                String
  theme               String?
  duration            String?
  intendedAudience    String?
  highlights          String[]
  priceOptions        Json
  upcomingDates       Json
}

model Policy {
  id                            String   @id @default(cuid())
  chatbotId                     String   @unique
  chatbot                       Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  codeOfConduct                 String?   @db.Text
  paymentPolicy                 String?   @db.Text
  cancellationAndRefundPolicy   String?   @db.Text
}

model FAQ {
  id          String   @id @default(cuid())
  chatbotId   String
  chatbot     Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  question    String
  answer      String   @db.Text
}


// ===================================
// 4. Chat History & Visitor Models
// (Unchanged from previous final plan)
// ===================================

model ChatSession {
  id            String    @id @default(cuid())
  chatbotId     String
  chatbot       Chatbot   @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  visitorId     String
  visitor       Visitor   @relation(fields: [visitorId], references: [id])
  controller    String    @default("LLM") // Can be "LLM" or "USER"
  ablyChannel   String    @unique
  tokenCount    Int       @default(0)

  messages      Message[]
  createdAt     DateTime  @default(now())
}

model Message {
  id            String      @id @default(cuid())
  chatSessionId String
  chatSession   ChatSession @relation(fields: [chatSessionId], references: [id], onDelete: Cascade)
  senderType    String      // "VISITOR", "LLM", or "USER"
  content       String      @db.Text
  systemData    Json?       // For debugging: stores raw LLM tool calls, prompts, etc.
  createdAt     DateTime    @default(now())
}

model Visitor {
  id        String   @id @default(cuid())
  email     String?
  name      String?
  profileData Json?
  chatSessions ChatSession[]
}
```

---

### **Part 2: Phased Development Plan & Coder Instructions**

**To the AI Developer:**

Welcome to the YogaBot Live project. This document outlines your development tasks, structured in a new 6-phase plan that reflects the full SaaS architecture. Your goal is to implement the features as described, focusing on clean, secure, and maintainable code. The Project Lead has already completed the initial infrastructure setup.

#### **Global Foundational Tasks (To Be Applied Across All Phases)**

*   ✅ **Universal API Authorization:** Create a utility function (e.g., in `/lib/auth.ts`) to get the current session and verify the user's role and ownership of the requested resource. This must be used on all protected API routes.
*   ✅ **Strict Input Validation:** Use the `zod` library to parse and validate the `req.body` and `req.params` of every API route.
*   ✅ **Standardized Error Handling:** Wrap all external API calls and critical logic in `try...catch` blocks. Return consistent error responses and use appropriate HTTP status codes.

---

### **Phase 1: The Core SaaS Foundation**

**Goal:** Build the application's core data models and business logic for a plan-based SaaS. No chat functionality will be built in this phase.

**Tasks:**

1.  **Install Dependencies:** `npm install next-auth @next-auth/prisma-adapter bcryptjs zod`
2.  **Schema & Migration:**
    *   Implement the complete new schema in `/prisma/schema.prisma` as detailed above.
    *   Run `npx prisma migrate dev --name "v2-init-saas-architecture"`.
3.  **Implement Admin Plan Management:**
    *   Create a new page at `/admin/plans`.
    *   Build a UI for full CRUD (Create, Read, Update, Delete) management of `Plan` records. The form for creating/editing a plan must include fields for name, price, and all the JSON `features` (kbType, tokenLimit, canUseBYOK, etc.).
    *   Create the corresponding authorized API routes (`/api/admin/plans`) to manage these records.
4.  **Implement Admin User & Subscription Management:**
    *   Create a new page at `/admin/users`.
    *   Build a UI for creating a `User` and simultaneously creating their `Subscription`, linking them to a `Plan`.
    *   Create the corresponding API route (`/api/admin/users/create`) to handle this setup. This is where you will also create the initial `Chatbot` record for the user.
5.  **Implement Advanced Middleware (`/middleware.ts`):**
    *   Implement the authentication logic to protect routes.
    *   Enhance it: For API routes that consume resources (like sending a chat message), the middleware or a dedicated utility must perform these checks:
        1.  Fetch the user's `Subscription`.
        2.  Check if `status` is "active".
        3.  Check if `tokensUsedThisPeriod` < `plan.features.tokenLimit`.
        4.  If any check fails, return a `403 Forbidden` error with a clear message.

---

### **Phase 2: The Dual Knowledge Base System**

**Goal:** Build the user-facing UIs and backend pipelines for both Knowledge Base types.

**Tasks:**

1.  **Build the User Dashboard UI:**
    *   Create the main page at `/dashboard/kb`.
    *   This page's server component will fetch the user's plan. Based on `plan.features.kbType`, it will conditionally render ONE of the two client components below.
2.  **Implement the "Simple Text" KB:**
    *   Create the UI component: a large, simple textarea.
    *   Create an API route (`/api/kb/save-simple`) that takes the text, saves it to the `simpleKbText` field on the `Chatbot`, and then triggers a background job.
    *   The background job will chunk this simple text, generate embeddings, and populate the `KnowledgeBaseChunk` table for this chatbot (wiping old chunks first).
3.  **Implement the "Structured Form" KB:**
    *   Create the UI component: a complex, multi-tabbed form that mirrors the structure of `form.md`. Use a UI library like Radix UI or Shadcn/UI to manage tabs and repeatable form blocks.
    *   Create an API route (`/api/kb/save-structured`) that accepts the entire form's data. This API will perform `upsert` operations on all the related structured KB models (`SchoolBrand`, `Teacher`, etc.).
    *   After saving the structured data, this API will trigger a background job. The job's purpose is to read from all the structured tables, intelligently format the data into human-readable text chunks, generate embeddings for each chunk, and populate the `KnowledgeBaseChunk` table (wiping old chunks first).

---

### **Phase 3: The Chat Engine, Prompting & Usage Tracking**

**Goal:** Build the end-to-end chat functionality, connecting the visitor widget to the new, intelligent backend.

**Tasks:**

1.  **Build the Embeddable Chat Widget (`/app/widget/[botId]/page.tsx`):**
    *   Build the complete chat UI. Implement server-side **Domain Locking** by checking the `Referer` header.
2.  **Implement the Core Chat API (`/api/chatbot/sendMessage/route.ts`):**
    *   This API is now highly intelligent. It must:
        1.  Perform the usage limit check (or rely on the middleware to do it).
        2.  Create/retrieve the `Visitor` and `ChatSession`.
        3.  **Implement the Hierarchical Prompt Engine:**
            *   Fetch the user's `Plan` and the chatbot's `systemPrompt` (Admin-set).
            *   Fetch the active `Persona` (User-set).
            *   If `plan.features.kbType === 'structured'`, perform a semantic search against the `KnowledgeBaseChunk` table to get the most relevant context.
            *   If `plan.features.kbType === 'simple'`, fetch the raw `simpleKbText` and use it as context.
            *   Assemble the final prompt in the correct order: `System Prompt + Persona + (KB Chunks OR Simple Text) + Chat History + Visitor Question`.
        4.  Call the appropriate LLM using the LLM Adapter.
        5.  Save the `Message` records for both the visitor and the AI.
3.  **Implement Usage Tracking:**
    *   After a successful LLM call, parse the token count from the LLM's response.
    *   Increment the `tokenCount` on the `ChatSession` model.
    *   Atomically increment the `tokensUsedThisPeriod` on the user's `Subscription` model. Use Prisma's atomic operations (`increment`) to prevent race conditions.

---

### **Phase 4: Real-Time Layer & Live Chat Takeover**

**Goal:** Implement the live chat monitoring and takeover feature. (This phase is functionally identical to the previous final plan).

**Tasks:**

1.  **Install Dependency & Schema:** `npm install ably`. The schema is already prepared.
2.  **Implement Secure Ably Token Authentication:** Create an API route (`/api/ably/token`) to issue temporary, scoped tokens to clients. The master `ABLY_API_KEY` must never be exposed on the client side.
3.  **Implement Client-Side Real-Time Connections:** Use the token auth flow to connect the widget and the user dashboard to Ably.
4.  **Implement the Live Chat Takeover Backend Logic:**
    *   Create API routes for `/api/chat/takeover` and `/api/chat/user-send`.
    *   Modify `/api/chatbot/sendMessage` to act as the central router, checking the `controller` field (`"LLM"` or `"USER"`) before processing a message.
5.  **Integrate Presence Indicators:** Use Ably's Presence feature to show which visitors are currently online in the user's dashboard.

---

### **Phase 5: Advanced Settings & Admin Overrides**

**Goal:** Build the user-facing settings and the powerful Admin override controls.

**Tasks:**

1.  **Build User Settings Page (`/dashboard/settings`):**
    *   Create a UI where the user can manage their profile, `widgetConfig`, and `smtpConfig`.
    *   Conditionally render the "Bring Your Own Key" (BYOK) section only if `plan.features.canUseBYOK` is true.
    *   **SECURITY:** The API for saving the BYOK key must use `AES-256-GCM` encryption.
2.  **Build Admin Chatbot Management UI:**
    *   Create a new page at `/admin/chatbots/[chatbotId]`.
    *   This page should allow the Admin to view and edit all fields on a specific `Chatbot` record.
    *   This includes:
        *   A textarea to edit the `systemPrompt`.
        *   Dropdowns to select the `llmProvider` and `llmModel`.
        *   An input to set/override the user's `encryptedLlmApiKey`.
3.  **Update the LLM Adapter:** The adapter (`/lib/llm.ts`) must now prioritize keys in this order:
    1.  Use the `Chatbot.encryptedLlmApiKey` if it exists (BYOK).
    2.  Else, use the platform's master keys from the environment variables, selecting the correct one based on `Chatbot.llmProvider`.

---

### **Phase 6: Billing Integration**

**Goal:** Automate the subscription and payment process.

**Tasks:**

1.  **Build Public Pricing Page:** Create a page that displays the `Plan` details to prospective customers.
2.  **Implement Razorpay Checkout:** Integrate Razorpay's client-side library to handle the checkout flow when a user selects a plan.
3.  **Implement Backend Subscription Creation:** Create an API route that initiates the subscription in Razorpay and creates the `Subscription` record in your database.
4.  **Implement Critical Webhook Endpoint (`/api/webhooks/razorpay`):**
    *   This is the most critical part of the billing system.
    *   It must securely listen for events from Razorpay (e.g., `subscription.charged`, `payment.failed`).
    *   Based on these events, it must update the `status` and `currentPeriodEnd` of the `Subscription` record in your database. This ensures a user's access is automatically granted or revoked based on their payment status.

---

### **Part 3: Initial Project Setup (Checklist for Project Lead - Confirmed Complete)**

**Note for the AI Coder:** The Project Lead has completed the following foundational work. Your tasks begin with Phase 1 of the new development plan.

*   [x] **Code & Version Control:** New Next.js project created and pushed to a private GitHub repository.
*   [x] **Hosting & CI/CD:** Vercel project created and connected to the GitHub repository.
*   [x] **Database:** Vercel Postgres database provisioned.
*   [x] **Database Extension:** The `pgvector` extension has been confirmed as active in the Vercel Postgres database.
*   [x] **Environment Variables:** `POSTGRES_PRISMA_URL` and all third-party API keys added to Vercel project settings and a local `.env.local` file.
*   [x] **Third-Party Services:** Accounts for Ably and Upstash QStash have been created.
*   [x] **Prisma Initialization:** `npx prisma init` has been run.

(Note from User : I have completed initial project setup, please read everything carefully and then create a todo file where you list everything that needs to be done to complete the project, you will be reading that file for any future tasks to see whats implemented whats remaining and then implement them based on final-plan.md file, with these tasks in the todo.md file add check boxes to mark them complete or pending and update them with every change you make, for the issues related to postgresql you can use postgresql and pgvector from local macbook, check if they exist, check if they are running then use them)

#### **Reference `.env.local` Structure**

The .env.local file if fully populated with accurate data

```
# .env.local
# This file contains all secret keys for LOCAL DEVELOPMENT.
# This file MUST be in your .gitignore and NEVER committed to version control.

# DATABASE
# Connection string for your Vercel PostgreSQL database, formatted for Prisma.
POSTGRES_PRISMA_URL=""

# AUTHENTICATION
# A long, random secret string used by NextAuth.js to sign session data.
# Generate one with `openssl rand -base64 32` in your terminal.
NEXTAUTH_SECRET=""

# The full URL of your local development server. Required by NextAuth.js.
NEXTAUTH_URL="http://localhost:3000"

# CORE SERVICES & LLMs
# Your Google AI Studio API key for using the Gemini models.
# This will be the default LLM provider for the application.
GEMINI_API_KEY=""

# Your API key for OpenRouter.ai.
# This provides flexibility to use other models.
OPENROUTER_API_KEY=""

# A secret key used to encrypt and decrypt sensitive user data, like SMTP passwords.
# Use the same generator as NEXTAUTH_SECRET to create another random 32-character string.
ENCRYPTION_KEY=""

# Your API key from the Ably dashboard for the real-time chat service.
# This is the MASTER KEY and should only be used on the server.
ABLY_API_KEY=""

# BACKGROUND JOB QUEUE
# Your API tokens from the Upstash QStash dashboard.
QSTASH_URL="https://qstash.upstash.io"
QSTASH_TOKEN=""
QSTASH_CURRENT_SIGNING_KEY=""
QSTASH_NEXT_SIGNING_KEY=""
```