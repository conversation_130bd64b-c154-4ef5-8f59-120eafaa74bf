# YogaBot Project Todo List

This document tracks the implementation progress of the YogaBot project based on the final-plan.md specification.

## Phase 1: The Core SaaS Foundation ✅

- [x] **Install Dependencies**
  - [x] Install next-auth, @next-auth/prisma-adapter, bcryptjs, zod

- [x] **Schema & Migration**
  - [x] Implement complete schema in `/prisma/schema.prisma`
  - [x] Run migration `npx prisma migrate dev --name "v2-init-saas-architecture"`

- [x] **Implement Admin Plan Management**
  - [x] Create `/admin/plans` page
  - [x] Build UI for CRUD operations on `Plan` records
  - [x] Create API routes `/api/admin/plans` for plan management

- [x] **Implement Admin User & Subscription Management**
  - [x] Create `/admin/users` page
  - [x] Build UI for creating `User` and `Subscription`
  - [x] Create API route `/api/admin/users/create`

- [x] **Implement Advanced Middleware**
  - [x] Create `/middleware.ts` with authentication logic
  - [x] Implement subscription status and usage checks

## Phase 2: The Dual Knowledge Base System ✅

- [x] **Build the User Dashboard UI**
  - [x] Create `/dashboard` main page
  - [x] Create `/dashboard/kb` page
  - [x] Implement conditional rendering based on plan type

- [x] **Implement the "Simple Text" KB**
  - [x] Create simple textarea UI component
  - [x] Create API route `/api/kb/save-simple`
  - [x] Implement basic text chunking functionality
  - [x] Implement proper embedding generation with transformers.js

- [x] **Implement the "Structured Form" KB**
  - [x] Create multi-tabbed form UI based on `form.md`
  - [x] Implement School Brand form tab
  - [x] Create API routes for fetching and saving brand data
  - [x] Implement School Contact form tab
  - [x] Create API routes for fetching and saving contact data
  - [x] Implement Teachers form tab
  - [x] Create API routes for fetching and saving teachers data
  - [x] Implement remaining form tabs (TTCs, Retreats, etc.)
    - [x] Implement Teacher Training Courses (TTC) form tab
    - [x] Create API routes for fetching and saving TTC data
    - [x] Implement Retreats form tab
    - [x] Create API routes for fetching and saving Retreat data
    - [x] Implement Online Offerings form tab
    - [x] Create API routes for fetching and saving Online Course data
    - [x] Implement Facilities form tab
    - [x] Create API routes for fetching and saving Facility data
    - [x] Implement Policies form tab
    - [x] Create API routes for fetching and saving Policy data
    - [x] Implement FAQ form tab
    - [x] Create API routes for fetching and saving FAQ data
  - [x] Implement background job for structured data processing

## Phase 3: The Chat Engine, Prompting & Usage Tracking

- [x] **Build the Embeddable Chat Widget**
  - [x] Create `/app/widget/[botId]/page.tsx`
  - [x] Implement domain locking via Referer header check

- [x] **Implement the Core Chat API**
  - [x] Create `/api/chatbot/sendMessage/route.ts`
  - [x] Implement usage limit checks
  - [x] Build visitor and chat session management
  - [x] Create hierarchical prompt engine
  - [x] Implement LLM adapter integration (mock for now)
  - [x] Save message records

- [x] **Implement Usage Tracking**
  - [x] Parse token count from LLM responses
  - [x] Update session token count
  - [x] Update subscription usage with atomic operations

## Phase 4: Real-Time Layer & Live Chat Takeover ✅

- [x] **Install Ably Dependency**
  - [x] `npm install ably`

- [x] **Implement Secure Ably Token Authentication**
  - [x] Create API route `/api/ably/token`

- [x] **Implement Client-Side Real-Time Connections**
  - [x] Connect widget to Ably
  - [x] Connect user dashboard to Ably
  - [x] Create useAbly custom hook

- [x] **Implement Live Chat Takeover Backend Logic**
  - [x] Create API routes `/api/chat/takeover` and `/api/chat/user-send`
  - [x] Modify `/api/chatbot/sendMessage` to check controller field

- [x] **Integrate Presence Indicators**
  - [x] Use Ably's Presence feature for online status
  - [x] Create live chat dashboard for monitoring sessions

## Phase 5: Advanced Settings & Admin Overrides

- [ ] **Build User Settings Page**
  - [ ] Create `/dashboard/settings` page
  - [ ] Implement profile, widget config, and SMTP config management
  - [ ] Add conditional BYOK section with AES-256-GCM encryption

- [ ] **Build Admin Chatbot Management UI**
  - [ ] Create `/admin/chatbots/[chatbotId]` page
  - [ ] Implement system prompt editing
  - [ ] Add LLM provider and model selection
  - [ ] Create API key override functionality

- [ ] **Update the LLM Adapter**
  - [ ] Modify `/lib/llm.ts` to prioritize keys correctly

## Phase 6: Billing Integration

- [ ] **Build Public Pricing Page**
  - [x] Create home page with pricing information
  - [ ] Create dedicated pricing page

- [ ] **Implement Razorpay Checkout**
  - [ ] Integrate Razorpay client-side library

- [ ] **Implement Backend Subscription Creation**
  - [ ] Create API route for Razorpay subscription creation

- [ ] **Implement Critical Webhook Endpoint**
  - [ ] Create `/api/webhooks/razorpay` endpoint
  - [ ] Handle subscription events and update database

## Global Foundational Tasks

- [x] **Universal API Authorization**
  - [x] Create utility function in `/lib/auth.ts`

- [x] **Strict Input Validation**
  - [x] Use zod for all API route validation

- [x] **Standardized Error Handling**
  - [x] Implement consistent error responses

## Additional Required Dependencies to Install

- [x] Prisma Client
- [ ] Ably
- [x] Upstash QStash
- [ ] Nodemailer
- [x] UI libraries (Shadcn/UI)
- [x] Xenova/transformers.js
- [ ] AES-256-GCM encryption library
- [ ] Razorpay client
- [x] UUID

## Notes

### Implementation Progress
- **Phase 1 Complete**: We've successfully implemented the core SaaS foundation with user authentication, admin management of plans and users, and the database schema.
- **Authentication**: Implemented NextAuth.js with credentials provider and session-based authentication.
- **Admin Panel**: Created admin dashboard with plan management and user creation functionality.
- **API Routes**: Implemented secure API routes with proper authorization and validation using Zod.
- **Middleware**: Created middleware for route protection and role-based access control.
- **Home Page**: Implemented a basic home page with pricing information.
- **User Dashboard**: Created a basic user dashboard showing subscription information.
- **Knowledge Base UI**: Implemented the Knowledge Base UI with conditional rendering based on plan type.
- **Simple Text KB**: Implemented the Simple Text KB UI and API endpoint with text chunking and embedding generation.
- **Embeddings**: Implemented text embedding generation using transformers.js for semantic search.
- **Structured Form**: Implemented the structured form with tabs and completed the School Brand, School Contact, and Teachers form sections.
- **Background Job Processing**: Implemented background job processing for structured data using Upstash QStash.
- **Chat Widget**: Implemented embeddable chat widget with domain locking.
- **Chat API**: Implemented core chat API with hierarchical prompt engine and usage tracking.
- **Real-Time Layer**: Implemented Ably integration for real-time messaging and live chat takeover.
- **Live Chat Dashboard**: Created dashboard for monitoring active chat sessions and taking over conversations.
- **Presence System**: Implemented presence indicators to show when agents are online and available.

### Technical Deviations
- **Database**: ~~We're currently using **SQLite** instead of **PostgreSQL** as specified in the plan. This deviation was necessary due to connection issues with the PostgreSQL setup.~~
  - ~~**Reason**: Unable to establish a reliable connection to the Vercel Postgres instance during development.~~
  - ~~**Impact**: This is a temporary solution for development purposes only.~~
  - ~~**Rectification Plan**: Before deployment to production, we need to:~~
    1. ~~Update the Prisma schema provider from "sqlite" back to "postgresql"~~
    2. ~~Ensure proper connection string format for Vercel Postgres~~
    3. ~~Test the PostgreSQL connection thoroughly~~
    4. ~~Run a fresh migration on the production database~~
  - **Fixed**: Successfully migrated from SQLite to PostgreSQL with pgvector support.

- **Vector Database**: ~~The current SQLite implementation doesn't support the vector extension needed for semantic search.~~
  - ~~**Impact**: We've implemented a workaround for vector embeddings in SQLite during development.~~
  - ~~**Solution**: Storing embeddings as serialized JSON strings in SQLite and implementing application-level vector search.~~
  - ~~**Rectification Plan**: When switching to PostgreSQL, update the schema to use proper vector type.~~
  - **Fixed**: Successfully implemented vector type with PostgreSQL and pgvector extension.

### Current Task
- [x] Reviewed and updated schema.prisma file to continue with SQLite for development
- [x] Implemented the Knowledge Base dashboard UI at `/dashboard/kb`
- [x] Installed UI library (Shadcn/UI) for building the structured form components
- [x] Implemented the Simple Text KB UI and API endpoint
- [x] Implemented proper embedding generation with transformers.js
- [x] Implemented the School Brand form tab and API endpoints
- [x] Implemented the School Contact form tab and API endpoints
- [x] Implemented the Teachers form tab and API endpoints
- [x] Implemented the Teacher Training Courses (TTC) form tab and API endpoints
- [x] Implemented the Retreats form tab and API endpoints
- [x] Implemented the Online Offerings form tab and API endpoints
- [x] Implemented the Facilities form tab and API endpoints
- [x] Implemented the Policies form tab and API endpoints
- [x] Implemented the FAQ form tab and API endpoints
- [x] Completed all form tabs for the structured knowledge base
- [x] Fixed the PostgreSQL and pgvector deviations
- [x] Implemented background job for structured data processing
- [x] Implemented the embeddable chat widget
- [x] Implemented the core chat API
- [x] Implemented Real-Time Layer & Live Chat Takeover in Phase 4
  - [x] Installed Ably dependency
  - [x] Created secure Ably token authentication
  - [x] Implemented client-side real-time connections
  - [x] Created useAbly custom hook for real-time messaging
  - [x] Updated chat widget to support real-time messaging and agent takeover
  - [x] Created live chat dashboard for monitoring and taking over sessions
  - [x] Implemented presence indicators and online status

### Next Steps
- [x] ~~Try to fix the PostgreSQL and pgvector deviations~~
- [x] ~~Try to fix the deviations, by imagining the required information is already present in the .env.local file as i do not have read write access to it but i can use it based on the information offered in the final-plan.md . if that does not work i will check if postgresql and pgvector is installed in the local mac system using homebrew, if it is installed i will ise it for the time being.~~
- [x] ~~Implement background job for structured data processing~~
- [x] ~~Implement the Chat Widget and Core Chat API in Phase 3~~
- [x] ~~Implement Real-Time Layer & Live Chat Takeover in Phase 4~~
- [ ] Implement Advanced Settings & Admin Overrides in Phase 5
  - [ ] Build User Settings Page
  - [ ] Build Admin Chatbot Management UI
  - [ ] Update the LLM Adapter

### Known Issues to Address
- Need to implement proper error handling for database connection failures
- Need to add comprehensive test coverage for the authentication system
- Need to implement proper input validation for all form submissions
- Some API routes have linter errors related to TypeScript types that need to be fixed
- The LLM integration is currently mocked and needs to be replaced with actual LLM API calls
